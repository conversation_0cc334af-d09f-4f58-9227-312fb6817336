import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { logger } from '@/lib/logger';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Update user to reactivate
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        isSuspended: false,
        suspensionReason: null,
        isActive: true,
        updatedAt: new Date(),
      }
    });

    // Create audit log
    await prisma.userAuditLog.create({
      data: {
        userId: userId,
        action: 'account.reactivate',
        entityType: 'user',
        entityId: userId,
        details: JSON.stringify({
          reactivatedAt: new Date().toISOString(),
          reactivatedBy: 'admin'
        }),
        ipAddress: request.headers.get('x-forwarded-for') || null,
        userAgent: request.headers.get('user-agent') || null,
      }
    });

    logger.info(`[admin] User ${userId} reactivated by admin`);

    return NextResponse.json({ 
      success: true, 
      message: 'User reactivated successfully' 
    });

  } catch (error) {
    logger.error('[admin] Error reactivating user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
