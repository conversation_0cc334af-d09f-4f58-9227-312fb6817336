"server-only"

import { Prisma } from "@/generated/prisma"
import { Decimal } from "@/generated/prisma/runtime/library"
import { getCurrentDbUser } from "@/lib/auth"
import { prisma, withRetry } from "@/lib/db"
import { logger } from "@/lib/logger"
import { redis } from "@/lib/redis"
import { getCachedData } from "@/lib/redis-cache"
import { toSafeNumber } from "@/lib/utils"
import { cuidSchema, userIdClerkSchema } from "@/lib/zod"
import { Cart } from "@/types/cart"
import { ProductCardInterface, RawOrderFromPrisma, RecentOrder } from "@/types/product"
import { cache } from "react"
import { z } from "zod"


export async function getUserIdFromDB(clerId: string){
  if (!clerId){
    logger.warn(`[getUserIdFromDB] No paramater provided`)
    return null
  }

  try{
    //const userIdParsed = cuidSchema.safeParse(userIdDb)
    const parse = userIdClerkSchema.safeParse(clerId)

    if (!parse.success) {
      logger.warn("[getUserIdFromDB] Invalid user ID format")
      return null
    }

    const userId = parse.data

    const userDB = await withRetry(() =>
          prisma.user.findUnique({
            where: { externalId: userId },
            select: {id: true}
          })
        );

    if(!userDB) return null

    return userDB

  }catch(e){
    logger.error(`[getUserIdFromDB] Error trying to get the wishlist count for clerId ${clerId}: ${e}`)
    return null
  }

}

export async function getRecentOrders(
  userId: string,
  limit = 8
): Promise<RecentOrder[]> {
  // 1️⃣ Validate input parameters using Zod
  const paramsValidation = cuidSchema.safeParse( userId );

  if (!paramsValidation.success) {
    logger.error(`[getRecentOrders] Invalid userId provided: ${userId}`)
    return []
  }

  const validatedUserId = paramsValidation.data;

  try {
    // 2️⃣ Fetch raw orders from Prisma
    // The type assertion `as RawOrderFromPrisma[]` is used here
    // to correctly type the result from Prisma based on our `select` statement.
    const raws = (await withRetry(() =>
      prisma.order.findMany({
        where: { userId: validatedUserId },
        orderBy: { createdAt: "desc" },
        take: limit,
        select: {
          orderItems: {
            select: {
              product: {
                select: {
                  id: true,
                  Material_Number: true,
                  Description_Local: true,
                  PretAM: true,
                  FinalPrice: true,
                  ImageUrl: true,
                  categoryLevel3: { select: { name: true } },
                  HasDiscount: true,
                  discountPercentage: true,
                  activeDiscountType: true,
                  activeDiscountValue: true,
                  productClass: {
                    select: {
                      vehicleModels: {
                        select: {
                          vehicleModel: { select: { name: true } },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      })
    )) as RawOrderFromPrisma[];

    if (!raws || !Array.isArray(raws)) {
      logger.warn(`[getRecentOrders] No valid orders found for userId ${validatedUserId}`);
      return [];
    }

    // 3️⃣ Manually map and transform raw data to safe types
    const safeOrders: RecentOrder[] = raws.map((rawOrder) => {
      return {
        orderItems: rawOrder.orderItems.map((rawOrderItem) => ({
          product: {
            id: rawOrderItem.product.id,
            Material_Number: rawOrderItem.product.Material_Number,
            Description_Local: rawOrderItem.product.Description_Local,
            PretAM: toSafeNumber(rawOrderItem.product.PretAM),
            FinalPrice: toSafeNumber(rawOrderItem.product.FinalPrice),
            ImageUrl: rawOrderItem.product.ImageUrl,
            categoryLevel3: rawOrderItem.product.categoryLevel3, 
            HasDiscount: rawOrderItem.product.HasDiscount,
            discountPercentage: toSafeNumber(rawOrderItem.product.discountPercentage),
            activeDiscountType: rawOrderItem.product.activeDiscountType,
            activeDiscountValue: toSafeNumber(rawOrderItem.product.activeDiscountValue),
            productClass: rawOrderItem.product.productClass
              ? { // Only map if productClass is not null
                  vehicleModels: rawOrderItem.product.productClass.vehicleModels.map(
                    (vm) => ({
                      vehicleModel: {
                        name: vm.vehicleModel.name,
                      },
                    })
                  ),
                }
              : null, // If raw productClass is null, assign null
          },
        })),
      };
    });

    return safeOrders;
  } catch (error) {
    console.error("Error fetching or transforming recent orders:", error);
    // Re-throw a generic error or a more specific custom error depending on your app's needs
    throw new Error("Failed to retrieve recent orders due to a server error.");
  }
}




