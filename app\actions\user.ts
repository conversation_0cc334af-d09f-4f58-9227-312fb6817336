import { getCurrentDbUser } from "@/lib/auth"
import { prisma, withRetry } from "@/lib/db"
import { headers } from "next/headers"


// Toggle wishlist item (add/remove)
export async function toggleWishlistItem(productCode: string) {
  const dbUser = await getCurrentDbUser()

  const headerPayload = await headers()

  if (!dbUser) return { success: false, error: "Not authenticated" }
  
  try {
    // Check if item exists in wishlist
    const existingItem = await withRetry(() => prisma.wishlist.findUnique({
      where: {
        userId_productCode: {
          userId: dbUser.id,
          productCode
        }
      }
    }))
    
    if (existingItem) {
      // Remove from wishlist
      await withRetry(() => prisma.wishlist.delete({
        where: {
          userId_productCode: {
            userId: dbUser.id,
            productCode
          }
        }
      }))
      // Log the action
      await withRetry(() => prisma.userAuditLog.create({
        data: {
          userId: dbUser.id,
          action: "wishlist.remove",
          entityType: "wishlist",
          entityId: existingItem.id,
          details: JSON.stringify({ productCode }),
          ipAddress: headerPayload.get('x-forwarded-for') || null,
          userAgent: headerPayload.get('user-agent') || null,
        }
      }))

      return { success: true, action: "removed" }
    } else {
      // Add to wishlist
      await prisma.wishlist.create({
        data: {
          userId: dbUser.id,
          productCode
        }
      })
      return { success: true, action: "added" }
    }
  } catch (error) {
    console.error("Wishlist operation failed:", error)
    return { success: false, error: "Operation failed" }
  }
}