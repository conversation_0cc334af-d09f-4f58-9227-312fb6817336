import {
  CheckCircle,
  Package,
  Truck,
  Calendar,
  ArrowRight,
  Home,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";
import { getCurrentDbUser } from "@/lib/auth";
import { checkOrderOwnership } from "../getData/orders";

type SearchParams = Promise<{ [key: string]: string }>
 

export default async function OrderConfirmationPage(props: {searchParams: SearchParams
}) {
    const searchParameter = await props.searchParams;
    const orderNumber = searchParameter.order || "ORD-2024-001-default";
    if (!orderNumber) return notFound();

    const user = await getCurrentDbUser();
    if (!user) redirect("/sign-in");

    const orderBelongsToUser = await checkOrderOwnership(user.id, orderNumber);
    if (!orderBelongsToUser) return notFound();

    return (
        <div className="min-h-screen  flex items-center justify-center px-4">
        <div className="max-w-4xl mx-auto text-center">
            {/* Success Icon */}
            <div className="mb-8">
            <div className="inline-flex items-center justify-center w-24 h-24  rounded-full mb-6 animate-bounce">
                <CheckCircle className="w-12 h-12 text-green-600" />
            </div>
            </div>

            {/* Success Message */}
            <div className="mb-12 space-y-4">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-brand mb-4">
                Comanda confirmata!
            </h1>
            <p className="text-xl  max-w-2xl mx-auto leading-relaxed">
                Mulțumim pentru achiziție! Comanda ta de piese BMW a fost plasată cu succes și este în curs de procesare.
            </p>
            </div>

            {/* Order Details Card */}
            <Card className="max-w-2xl mx-auto mb-8 shadow-xl border-0">
            <CardContent className="p-8">
                <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <h3 className="text-2xl font-bold text-gray-brand">
                    Detalii Comanda
                    </h3>
                    <Badge className="bg-green-100 text-green-800 px-4 py-2 text-sm font-semibold">
                    Comanda confirmata
                    </Badge>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
                    <div className="space-y-2">
                    <p className="text-sm text-muted-foreground uppercase tracking-wide font-semibold">
                        Numar Comanda
                    </p>
                    <p className="text-lg font-bold text-blue-brand">
                        {orderNumber}
                    </p>
                    </div>
                    <div className="space-y-2">
                    <p className="text-sm text-muted-foreground uppercase tracking-wide font-semibold">
                        Data Comenzii
                    </p>
                    <p className="text-lg font-semibold text-gray-brand">
                        {new Date().toLocaleDateString()}
                    </p>
                    </div>
                </div>

                {/* Order Timeline */}
                <div className="border-t pt-6">
                    <h4 className="text-lg font-semibold text-gray-brand mb-4">
                    Care este urmatorul pas?
                    </h4>
                    <div className="space-y-4">
                    <div className="flex items-center gap-4">
                        <div className="w-8 h-8 bg-blue-brand rounded-full flex items-center justify-center">
                        <Package className="w-4 h-4 text-white" />
                        </div>
                        <div className="text-left">
                        <p className="font-semibold text-gray-brand">Procesare</p>
                        <p className="text-sm text-muted-foreground">
                            Pregătim piesele tale BMW.
                        </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-4 opacity-60">
                        <div className="w-8 h-8 bg-slate-200 rounded-full flex items-center justify-center">
                            <Truck className="w-4 h-4 text-muted-foreground" />
                        </div>
                        <div className="text-left">
                        <p className="font-semibold text-gray-brand">Livrare</p>
                        <p className="text-sm text-muted-foreground">
                            Estimativ 2–3 zile lucrătoare
                        </p>
                        </div>
                    </div>
                    {/* <div className="flex items-center gap-4 opacity-60">
                        <div className="w-8 h-8 bg-slate-200 rounded-full flex items-center justify-center">
                        <Calendar className="w-4 h-4 text-slate-400" />
                        </div>
                        <div className="text-left">
                        <p className="font-semibold text-slate-400">Delivery</p>
                        <p className="text-sm text-slate-400">
                            Right to your doorstep
                        </p>
                        </div>
                    </div> */}
                    </div>
                </div>
                </div>
            </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Link href="/account/orders" >
                <Button
                    className="bg-blue-brand hover:bg-blue-brand-dark text-white px-8 py-3 text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                    size="lg"
                >
                    <Package className="w-5 h-5 mr-2" />
                    Vezi comanda
                </Button>
            </Link>

            <Link href="/">
                <Button
                    //onClick={() => navigate("/")}
                    variant="outline"
                    className="border-blue-brand text-blue-brand hover:bg-blue-brand hover:text-white px-8 py-3 text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                    size="lg"

                    
                >
                    <Home className="w-5 h-5 mr-2" />
                    Continua cumparaturile tale
                </Button>    
            </Link>    
            <div className="flex items-center justify-center gap-3 mb-4">
                <div className="w-3 h-3 bg-blue-brand rounded-full animate-pulse"></div>
                <h3 className="text-lg font-bold text-gray-brand">
                Email de confirmare trimis
                </h3>
            </div>
            <p className="text-muted-foreground">
                Ți-am trimis un email cu detaliile complete ale comenzii. Vei primi informațiile de tracking imediat ce coletul va fi expediat.
            </p>
            </div>

            {/* Premium Badge */}
            <div className="mt-12">
            <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-brand to-gray-brand rounded-full text-white font-semibold shadow-lg">
                <div className="w-2 h-2 bg-white rounded-full mr-3 animate-pulse"></div>
                Piese BMW originale • Calitate premium • Plată securizată
            </div>
            </div>
        </div>
        </div>
    );
}


// return (
//   <div className="min-h-screen bg-gradient-to-br 
//                   from-background to-muted/20 
//                   dark:from-muted/40 dark:to-background
//                   flex items-center justify-center px-4">
//     <div className="max-w-4xl mx-auto text-center">

//       {/* Success icon */}
//       <div className="mb-8">
//         <div className="inline-flex items-center justify-center w-24 h-24 
//                         bg-green-100 dark:bg-green-900/20 rounded-full mb-6 animate-bounce">
//           <CheckCircle className="w-12 h-12 text-green-600 dark:text-green-400" />
//         </div>
//       </div>

//       {/* Headline + sub-copy */}
//       <div className="mb-12 space-y-4">
//         <h1 className="text-4xl md:text-5xl font-bold text-foreground">
//           Comanda confirmata!
//         </h1>
//         <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
//           Multumim pentru achizitie! Comanda ta de piese&nbsp;BMW a fost plasata
//           cu succes și este in curs de procesare.
//         </p>
//       </div>

//       {/* Order card */}
//       <Card className="max-w-2xl mx-auto mb-8 shadow-xl bg-card border border-border">
//         <CardContent className="p-8 space-y-6">

//           <div className="flex items-center justify-between">
//             <h3 className="text-2xl font-bold text-foreground">Detalii comanda</h3>

//             <Badge className="bg-green-100 dark:bg-green-900/20 
//                                text-green-800 dark:text-green-300
//                                px-4 py-2 text-sm font-semibold">
//               Comanda confirmata
//             </Badge>
//           </div>

//           <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
//             {/* # comanda */}
//             <div>
//               <p className="text-sm uppercase tracking-wide font-semibold text-muted-foreground">
//                 Numar comanda
//               </p>
//               <p className="text-lg font-bold text-primary">{orderNumber}</p>
//             </div>

//             {/* data */}
//             <div>
//               <p className="text-sm uppercase tracking-wide font-semibold text-muted-foreground">
//                 Data comenzii
//               </p>
//               <p className="text-lg font-semibold text-foreground">
//                 {new Date().toLocaleDateString("ro-RO")}
//               </p>
//             </div>
//           </div>

//           {/* Timeline */}
//           <div className="border-t border-border pt-6">
//             <h4 className="text-lg font-semibold text-foreground mb-4">
//               Care este urmatorul pas?
//             </h4>

//             <div className="space-y-4">
//               <div className="flex items-center gap-4">
//                 <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
//                   <Package className="w-4 h-4 text-white" />
//                 </div>
//                 <div className="text-left">
//                   <p className="font-semibold text-foreground">Procesare</p>
//                   <p className="text-sm text-muted-foreground">
//                     Pregatim piesele tale&nbsp;BMW.
//                   </p>
//                 </div>
//               </div>

//               {/* Delivery greyed out */}
//               <div className="flex items-center gap-4 opacity-60">
//                 <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
//                   <Truck className="w-4 h-4 text-muted-foreground" />
//                 </div>
//                 <div className="text-left">
//                   <p className="font-semibold text-muted-foreground">Livrare</p>
//                   <p className="text-sm text-muted-foreground">
//                     Estimativ 2–3 zile lucratoare
//                   </p>
//                 </div>
//               </div>
//             </div>
//           </div>
//         </CardContent>
//       </Card>

//       {/* Buttons */}
//       <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
//         <Link href="/account/orders">
//           <Button className="bg-primary text-white px-8 py-3 text-lg font-semibold
//                              hover:bg-primary/90 transition duration-300">
//             <Package className="w-5 h-5 mr-2" />
//             Vezi comanda
//           </Button>
//         </Link>

//         <Link href="/">
//           <Button
//             variant="outline"
//             className="border-primary text-primary hover:bg-primary hover:text-white
//                        px-8 py-3 text-lg font-semibold transition duration-300"
//           >
//             <Home className="w-5 h-5 mr-2" />
//             Continua cumpărăturile
//           </Button>
//         </Link>

//         <div className="flex items-center justify-center gap-3 mb-4">
//           <div className="w-3 h-3 bg-primary rounded-full animate-pulse" />
//           <h3 className="text-lg font-bold text-foreground">Email de confirmare trimis</h3>
//         </div>

//         <p className="text-muted-foreground">
//           Ti-am trimis un email cu detaliile complete ale comenzii. Vei primi informațiile de
//           tracking imediat ce coletul va fi expediat.
//         </p>
//       </div>

//       {/* Premium badge */}
//       <div className="mt-12">
//         <div className="inline-flex items-center px-6 py-3 bg-primary rounded-full
//                         text-white font-semibold shadow-lg">
//           <div className="w-2 h-2 bg-white/80 rounded-full mr-3 animate-pulse" />
//           Piese BMW originale • Calitate premium • Plata securizata
//         </div>
//       </div>
//     </div>
//   </div>
// );

