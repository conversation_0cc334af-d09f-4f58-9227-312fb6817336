'use client';

import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, AlertCircle } from "lucide-react";

export default function MFADisablePage() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [isDisabling, setIsDisabling] = useState(false);
  const [error, setError] = useState<string | null>(null);


  useEffect(() => {
    if (isLoaded && !user) {
      router.push('/sign-in');
    }
  }, [isLoaded, user, router]);

  const handleDisableMFA = async () => {
    setIsDisabling(true);
    setError(null);

    try {
      // Use Clerk's UserProfile component for MFA management
      // This is the recommended approach for Clerk v5
      setError('Pentru a dezactiva 2FA, vă rugăm să accesați setările contului Clerk.');

      // Redirect to Clerk's user profile page for MFA management
      setTimeout(() => {
        // Open Clerk's user profile in a new tab
        window.open('/user-profile#/security', '_blank');

        // Close this popup after a delay
        setTimeout(() => {
          window.close();
          router.push('/account/settings?tab=security');
        }, 2000);
      }, 2000);

    } catch (err: any) {
      console.error('MFA disable error:', err);
      setError(err.message || 'A apărut o eroare la dezactivarea 2FA. Vă rugăm să încercați din nou.');
    } finally {
      setIsDisabling(false);
    }
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle>Dezactivare Autentificare 2FA</CardTitle>
          <CardDescription>
            Confirmați dezactivarea autentificării cu doi factori
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Atenție:</strong> Dezactivarea 2FA va reduce securitatea contului dvs.
              Recomandăm să mențineți această funcție activată.
            </AlertDescription>
          </Alert>

          <div className="space-y-3">
            <p className="text-sm text-muted-foreground">
              Pentru a dezactiva autentificarea cu doi factori, veți fi redirecționat la setările de securitate Clerk.
            </p>

            <Button
              variant="destructive"
              onClick={handleDisableMFA}
              disabled={isDisabling}
              className="w-full"
            >
              {isDisabling ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Se redirecționează...
                </>
              ) : (
                'Accesează setările 2FA'
              )}
            </Button>

            <Button
              variant="outline"
              onClick={() => {
                window.close();
                router.push('/account/settings?tab=security');
              }}
              className="w-full"
            >
              Anulează
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
