"server-only"

import { EnrichedWishlistItem } from "@/types/wishlist";
import WishlistProduct from "./WishlistProduct";
import Link from "next/link";

export default function WishlistPage({
  wishlistItems,
}: {
  wishlistItems: EnrichedWishlistItem[];
}) {
  return (
    <div className="max-w-[1640px] mx-auto p-4">
      <h1 className="text-2xl font-semibold mb-6">
        Favorite ({wishlistItems.length}{" "}
        {wishlistItems.length === 1 ? "produs" : "produse"})
      </h1>
      <div className="border rounded-lg overflow-hidden shadow">
          {wishlistItems.map((item) => (
              <WishlistProduct key={item.product.Material_Number} item={item} />
            ))}
      </div>
    </div>
  );
}

//initial
// export default async function WishlistPage({wishlistItems}: {wishlistItems: WishlistItems[]}){
  
//   return (
//       <div className="mb-16">
//         <h1 className="text-2xl font-semibold my-4">
//          Favorite ({wishlistItems.length} {wishlistItems.length === 1 ? "produs" : "produse"})
//         </h1>

//         <div className="border dark:border-gray-700 rounded-lg shadow">
//           { wishlistItems.map((item, key) => (
//             <WishlistProduct item={item} key={key} />
//           ))}
//         </div>
//       </div>
//   );
// };
