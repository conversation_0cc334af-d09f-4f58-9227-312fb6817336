"use client";

import { useState, useTransition } from "react";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { addObersvationsToCart } from "@/app/actions/cart";
import type { Cart } from "@/types/cart";

export default function CartObservatiiRoute({ cart }: { cart: Cart }) {
  const [notes, setNotes] = useState(cart.order?.notes ?? "");
  const [isPending, startTransition] = useTransition();

  const handleBlur = () => {
    startTransition(async () => {
        try{
            const response = await addObersvationsToCart(notes);

            if (response.success) {
                toast.success("Observațiile au fost actualizate.");
            } else {
                toast.error("Eșec la actualizare.");
            }
        } catch (err) {
            toast.error("Nu s-a putut actualiza observațiile.");
        }
    });
  };

  return (
    <div className="mt-4">
      <Label htmlFor="notes" className="block text-sm font-medium ">
        Observații
      </Label>
      <Textarea
        id="notes"
        rows={3}
        value={notes}
        disabled={isPending}
        onChange={(e) => setNotes(e.target.value)}
        onBlur={handleBlur}
        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
      />
    </div>
  );
}
