// app/actions/wishlist.js (Server Actions)
'use server';

import { revalidatePath } from 'next/cache';
import { prisma, withRetry } from '@/lib/db';
import { auth } from '@clerk/nextjs/server';
import { logger } from '@/lib/logger';
import { productCodSchema, cuidSchema } from "@/lib/zod"
import { getCurrentDbUser } from '@/lib/auth';
import { redis } from '@/lib/redis';
import { delay } from '@/lib/utils';

export async function addToWishlist(productId: string) {

  if (!productId) return { success: false };

  const user = await getCurrentDbUser()
  if (!user) {
    logger.error(`[addToWishlist] No user authenticated`);
    return { success: false };
  }

  const userId = user.id

  const parsed = productCodSchema.safeParse(productId);
  if (!parsed.success) {
    logger.error("[addToWishlist] Invalid productCode:", parsed.error.format());
    return { success: false };
  }

  const productCode = parsed.data;

  try {
    const existing = await withRetry(() =>
      prisma.wishlist.findUnique({
        where: {
          userId_productCode: {
             userId,
            productCode,
          },
        },
      })
    );

    if (existing) {
      logger.info(`[addToWishlist] Already in wishlist: ${userId} - ${productCode}`);
      return { success: true };
    }

    await withRetry(() =>
      prisma.wishlist.create({
        data: {
          userId,
          productCode,
        },
      })
    );

    await redis?.del(`wishlist-count:${userId}`)
    //revalidatePath("/"); // or your specific wishlist path
    return { success: true };
  } catch (e) {
    logger.error(`[addToWishlist] Error for userId ${userId} with product ${productCode}`, e);
    return { success: false };
  }
}

export async function removeFromWishlist(productId: string) {
  if (!productId) return { success: false };

  const user = await getCurrentDbUser()
  if (!user) {
    logger.error(`[addToWishlist] No user authenticated`);
    return { success: false };
  }

  const userId = user.id

  const parsed = productCodSchema.safeParse(productId);
  if (!parsed.success) {
    logger.error("[removeFromWishlist] Invalid productCode:", parsed.error.format());
    return { success: false };
  }

  const productCode = parsed.data;

  try {
    const existing = await withRetry(() =>
      prisma.wishlist.findUnique({
        where: {
          userId_productCode: {
            userId,
            productCode,
          },
        },
      })
    );

    if (!existing) {
      logger.info(`[removeFromWishlist] No entry found: ${userId} - ${productCode}`);
      return { success: true };
    }

    await withRetry(() =>
      prisma.wishlist.delete({
        where: {
          userId_productCode: {
            userId,
            productCode,
          },
        },
      })
    );

    await redis?.del(`wishlist-count:${userId}`)
    revalidatePath("/"); // or your specific wishlist page
    return { success: true };
  } catch (error) {
    logger.error(`[removeFromWishlist] Failed to delete for userId ${userId} and productCode ${productCode}`, error);
    return { success: false };
  }
}
