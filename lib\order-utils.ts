import { 
  OrderStatus as PrismaOrderStatus,
  PaymentStatus as PrismaPaymentStatus,
  ShipmentStatus as PrismaShipmentStatus,
  PaymentMethod as PrismaPaymentMethod,
  ShippingMethod as PrismaShippingMethod
} from "@/generated/prisma";
import { 
  ORDER_STATUS_LABELS,
  PAYMENT_STATUS_LABELS,
  SHIPMENT_STATUS_LABELS,
  PAYMENT_METHOD_LABELS,
  SHIPPING_METHOD_LABELS
} from "@/types/orders";
import { CheckCircle, Clock, Package, Truck, XCircle, AlertCircle } from "lucide-react";

/**
 * Get the appropriate icon for order status
 */
export function getOrderStatusIcon(status: PrismaOrderStatus) {
  switch (status) {
    case "livrata":
    case "completa":
      return CheckCircle;
    case "expediata":
      return Truck;
    case "procesare":
    case "confirmata":
    case "pregatita":
      return Package;
    case "anulata":
    case "stornata":
      return XCircle;
    case "returnata":
      return AlertCircle;
    default:
      return Clock;
  }
}

/**
 * Get the appropriate color class for order status
 */
export function getOrderStatusColor(status: PrismaOrderStatus): string {
  switch (status) {
    case "livrata":
    case "completa":
      return "text-green-600 hover:text-green-700";
    case "expediata":
      return "text-blue-600 hover:text-blue-700";
    case "procesare":
    case "confirmata":
    case "pregatita":
      return "text-orange-600 hover:text-orange-700";
    case "anulata":
    case "stornata":
      return "text-red-600 hover:text-red-700";
    case "returnata":
      return "text-purple-600 hover:text-purple-700";
    default:
      return "text-gray-600 hover:text-gray-700";
  }
}

/**
 * Get the appropriate badge color class for order status
 */
export function getOrderStatusBadgeColor(status: PrismaOrderStatus): string {
  switch (status) {
    case "livrata":
    case "completa":
      return "bg-green-100 text-green-800 border-green-200";
    case "expediata":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "procesare":
    case "confirmata":
    case "pregatita":
      return "bg-orange-100 text-orange-800 border-orange-200";
    case "anulata":
    case "stornata":
      return "bg-red-100 text-red-800 border-red-200";
    case "returnata":
      return "bg-purple-100 text-purple-800 border-purple-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
}

/**
 * Get the appropriate icon for shipment status
 */
export function getShipmentStatusIcon(status: PrismaShipmentStatus) {
  switch (status) {
    case "livrat":
      return CheckCircle;
    case "expediat":
    case "tranzit":
      return Truck;
    case "prelucrare":
    case "pregatit":
      return Package;
    case "anulat":
    case "esuat":
      return XCircle;
    case "intors":
      return AlertCircle;
    default:
      return Clock;
  }
}

/**
 * Get the appropriate color class for shipment status
 */
export function getShipmentStatusColor(status: PrismaShipmentStatus): string {
  switch (status) {
    case "livrat":
      return "text-green-600 hover:text-green-700";
    case "expediat":
    case "tranzit":
      return "text-blue-600 hover:text-blue-700";
    case "prelucrare":
    case "pregatit":
      return "text-orange-600 hover:text-orange-700";
    case "anulat":
    case "esuat":
      return "text-red-600 hover:text-red-700";
    case "intors":
      return "text-purple-600 hover:text-purple-700";
    default:
      return "text-gray-600 hover:text-gray-700";
  }
}

/**
 * Check if an order can be cancelled
 */
export function canCancelOrder(orderStatus: PrismaOrderStatus): boolean {
  return ["plasata", "procesare", "confirmata"].includes(orderStatus);
}

/**
 * Check if an order can be returned
 */
export function canReturnOrder(orderStatus: PrismaOrderStatus): boolean {
  return ["livrata", "completa"].includes(orderStatus);
}

/**
 * Check if an order has tracking information
 */
export function hasTrackingInfo(orderStatus: PrismaOrderStatus, shipmentStatus: PrismaShipmentStatus): boolean {
  return ["expediat", "tranzit", "livrat"].includes(shipmentStatus) ||
         ["expediata", "livrata", "completa"].includes(orderStatus);
}

/**
 * Get user-friendly status label
 */
export function getStatusLabel(
  type: "order" | "payment" | "shipment" | "paymentMethod" | "shippingMethod",
  status: string
): string {
  switch (type) {
    case "order":
      return ORDER_STATUS_LABELS[status as PrismaOrderStatus] || status;
    case "payment":
      return PAYMENT_STATUS_LABELS[status as PrismaPaymentStatus] || status;
    case "shipment":
      return SHIPMENT_STATUS_LABELS[status as PrismaShipmentStatus] || status;
    case "paymentMethod":
      return PAYMENT_METHOD_LABELS[status as PrismaPaymentMethod] || status;
    case "shippingMethod":
      return SHIPPING_METHOD_LABELS[status as PrismaShippingMethod] || status;
    default:
      return status;
  }
}

/**
 * Format currency for display
 */
export function formatCurrency(amount: number, currency: string = "RON"): string {
  return new Intl.NumberFormat("ro-RO", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
  }).format(amount);
}

/**
 * Format date for display
 */
export function formatDate(dateString: string, options?: Intl.DateTimeFormatOptions): string {
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
  };
  
  return new Date(dateString).toLocaleDateString("ro-RO", options || defaultOptions);
}

/**
 * Format date and time for display
 */
export function formatDateTime(dateString: string): string {
  return new Date(dateString).toLocaleDateString("ro-RO", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
}

/**
 * Get order priority based on status and dates
 */
export function getOrderPriority(
  orderStatus: PrismaOrderStatus,
  paymentStatus: PrismaPaymentStatus,
  placedAt: string
): "high" | "medium" | "low" {
  const daysSincePlaced = Math.floor(
    (Date.now() - new Date(placedAt).getTime()) / (1000 * 60 * 60 * 24)
  );

  // High priority: unpaid orders older than 3 days or processing orders older than 7 days
  if (
    (paymentStatus === "asteptare" && daysSincePlaced > 3) ||
    (orderStatus === "procesare" && daysSincePlaced > 7)
  ) {
    return "high";
  }

  // Medium priority: recent orders or orders in progress
  if (
    daysSincePlaced <= 3 ||
    ["procesare", "confirmata", "pregatita", "expediata"].includes(orderStatus)
  ) {
    return "medium";
  }

  return "low";
}

/**
 * Generate mock tracking history for demonstration
 * In production, this would come from the shipping provider's API
 */
export function generateMockTrackingHistory(
  orderStatus: PrismaOrderStatus,
  shipmentStatus: PrismaShipmentStatus,
  placedAt: string
): Array<{
  date: string;
  status: string;
  location: string;
  description: string;
}> {
  const history = [];
  const placedDate = new Date(placedAt);

  // Order placed
  history.push({
    date: placedDate.toISOString(),
    status: "Order Placed",
    location: "Online",
    description: "Your order has been placed and is being processed",
  });

  if (["procesare", "confirmata", "pregatita", "expediata", "livrata", "completa"].includes(orderStatus)) {
    const processedDate = new Date(placedDate.getTime() + 24 * 60 * 60 * 1000);
    history.push({
      date: processedDate.toISOString(),
      status: "Order Processed",
      location: "Warehouse",
      description: "Your order has been processed and is being prepared for shipment",
    });
  }

  if (["expediata", "livrata", "completa"].includes(orderStatus)) {
    const shippedDate = new Date(placedDate.getTime() + 2 * 24 * 60 * 60 * 1000);
    history.push({
      date: shippedDate.toISOString(),
      status: "Shipped",
      location: "Distribution Center",
      description: "Package has been shipped from our warehouse",
    });
  }

  if (["livrata", "completa"].includes(orderStatus)) {
    const deliveredDate = new Date(placedDate.getTime() + 3 * 24 * 60 * 60 * 1000);
    history.push({
      date: deliveredDate.toISOString(),
      status: "Delivered",
      location: "Destination",
      description: "Package has been delivered successfully",
    });
  }

  return history.reverse(); // Most recent first
}
