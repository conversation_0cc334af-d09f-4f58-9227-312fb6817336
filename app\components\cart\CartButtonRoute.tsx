"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2, ShoppingCart } from "lucide-react";
import { addItemToCart } from "@/app/actions/cart";
import { toast } from "sonner";
import { useTransition } from "react";
import { useRouter } from "next/navigation"; // Import the router

export default function CartButtonRoute({ product }: { product: string }) {
  const [isPending, startTransition] = useTransition();
  const router = useRouter(); // Initialize the router

  const handleAddToCart = () => {
    startTransition(async () => {
      try {
        const response = await addItemToCart(product);
        if (response.success) {
          toast.success("Adăugat cu succes în coș.");
          router.refresh(); // Refresh the page to reflect the new cart state
        } else {
          toast.error("Nu s-a putut adăuga în coș.");
        }
      } catch (error) {
        toast.error("Eroare la actualizarea coșului.");
      }
    });
  };

  // The JSX for the button remains unchanged...
  return (
    <>
      {isPending ? (
        <Button 
          disabled 
          size="icon" 
          className="flex-1 bg-[#0066B1] hover:bg-[#004d85] text-white h-10"
        >
          <Loader2 className="h-5 w-5 animate-spin" /> 
        </Button>
      ) : (
        <Button 
          onClick={handleAddToCart}
          size="icon" 
          className="flex-1 bg-[#0066B1] hover:bg-[#004d85] text-white h-10"
        >
          <ShoppingCart className="w-5 h-5 mr-4" /> Adauga in cos
        </Button>
      )}
    </>
  );
}

            //   <Button className="flex-1 bg-[#0066B1] hover:bg-[#004d85] text-white h-10">
            //     Adauga in cos
            //   </Button>