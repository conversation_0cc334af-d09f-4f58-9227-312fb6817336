"server-only"

import AccountPage from "@/app/components/account/settings/AccountPage";
import ErrorBoundary from "@/app/components/account/settings/ErrorBoundary";
import { getCurrentDbUser, getUserSSOProvider } from "@/lib/auth";
import { getAccountSettingsData, getUserSecurityInfo, getUserAuditLogs } from "@/app/getData/account-settings";
import { redirect } from "next/navigation";
import { logger } from "@/lib/logger";
import { Suspense } from "react";
import { AccountSettingsLoading } from "@/app/components/account/settings/LoadingState";

export default async function AccountSettingsRoute() {
  try {
    // Get current authenticated user
    const user = await getCurrentDbUser();

    if (!user) {
      logger.warn('[AccountSettingsRoute] No authenticated user found');
      redirect("/sign-in");
    }

    // Fetch all required data in parallel for better performance
    const [accountData, securityInfo, auditLogs, ssoProvider] = await Promise.all([
      getAccountSettingsData(user.id),
      getUserSecurityInfo(user.id),
      getUserAuditLogs(user.id, 10),
      getUserSSOProvider()
    ]);

    if (!accountData) {
      logger.error(`[AccountSettingsRoute] Failed to fetch account data for user: ${user.id}`);
      redirect("/account-inactive");
    }

    return (
      <div className="max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8">
        <ErrorBoundary>
          <Suspense fallback={<AccountSettingsLoading />}>
            <AccountPage
              accountData={accountData}
              securityInfo={securityInfo}
              auditLogs={auditLogs}
              ssoProvider={ssoProvider}
            />
          </Suspense>
        </ErrorBoundary>
      </div>
    );
  } catch (error) {
    logger.error('[AccountSettingsRoute] Error in account settings route:', error);

    // Instead of redirecting to a generic error page, show a more specific error
    return (
      <div className="max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8">
        <ErrorBoundary>
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Eroare la încărcarea setărilor</h1>
            <p className="text-muted-foreground mb-6">
              Nu am putut încărca setările contului. Vă rugăm să încercați din nou.
            </p>
            <a
              href="/account/settings"
              className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
            >
              Încearcă din nou
            </a>
          </div>
        </ErrorBoundary>
      </div>
    );
  }
};
