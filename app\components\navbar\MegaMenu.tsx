"use client"

import React from "react";
import { motion } from "framer-motion";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Disc,
  Filter,
  Droplet,
  <PERSON>ch,
  Cog,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Plug,
} from "lucide-react";
import Image from "next/image";

interface SubCategory {
  id: string;
  name: string;
  href: string;
  //icon?: React.ReactNode;
  icon?: React.ReactElement<{className?: string}>;
  description?: string;
  color?: string;
  image?: string;
}

interface CategoryAndSubcategory {
  label: string;
  subcategories: SubCategory[];
}

interface MegaMenuProps {
  isOpen?: boolean;
  onClose?: () => void;
  defaultTab?: string;
}

const lifestyleCategories: SubCategory[] = [
  {
    id: "colectia-bmw-m",
    name: "Bmw M",
    href: "/Colectia-Bmw-M",
    image:
      "https://images.unsplash.com/photo-1614164185128-e4ec99c436d7?w=200&h=200&fit=crop",
    description: "For enthusiasts",
  },
  {
    id: "colectia-bmw",
    name: "Bmw",
    href: "/Colectia-Bmw",
    image:
      "https://images.unsplash.com/photo-1508685096489-7aacd43bd3b1?w=200&h=200&fit=crop",
    description: "Classic Bmw",
  },
  {
    id: "colectia-Bmw-i",
    name: "Bmw i",
    href: "/Colectia-Bmw-I",
    image:
      "https://images.unsplash.com/photo-1546868871-7041f2a55e12?w=200&h=200&fit=crop",
    description: "Innovative designs ",
  },
  {
    id: "colectia-Bmw-iconic",
    name: "Bmw Iconic",
    href: "/Colectia-Bmw-Iconic",
    image:
      "https://images.unsplash.com/photo-1614164185128-e4ec99c436d7?w=200&h=200&fit=crop",
    description: "Iconic collection",
  },
  {
    id: "colectia-Bmw-motorsport",
    name: "Bmw Motorsport",
    href: "/Colectia-Bmw-Motorsport",
    image:
      "https://images.unsplash.com/photo-1508685096489-7aacd43bd3b1?w=200&h=200&fit=crop",
    description: "Motorsport Bmw branded ",
  },
  {
    id: "colectia-Mini",
    name: "Mini",
    href: "/Colectia-Mini",
    image:
      "https://images.unsplash.com/photo-1546868871-7041f2a55e12?w=200&h=200&fit=crop",
    description: "Innovative designs ",
  },
  {
    id: "colectia-Bmw-Special",
    name: "Bmw Special",
    href: "/Colectia-Bmw-Special",
    image:
      "https://images.unsplash.com/photo-1614164185128-e4ec99c436d7?w=200&h=200&fit=crop",
    description: "Special Bmw  ",
  },
  {
    id: "colectia-Mini-Theme",
    name: "Mini Theme",
    href: "/Colectia-Mini-Theme",
    image:
      "https://images.unsplash.com/photo-1508685096489-7aacd43bd3b1?w=200&h=200&fit=crop",
    description: "Mini Theme  ",
  },
  {
    id: "colectia-Bmw-Athletics",
    name: "Bmw Athletics",
    href: "/Colectia-Bmw-Athletics",
    image:
      "https://images.unsplash.com/photo-1546868871-7041f2a55e12?w=200&h=200&fit=crop",
    description: "Athletics designs ",
  },
  {
    id: "colectia-Bmw-Yachtsport",
    name: "Bmw Yachtsport",
    href: "/Colectia-Bmw-Yachtsport",
    image:
      "https://images.unsplash.com/photo-1614164185128-e4ec99c436d7?w=200&h=200&fit=crop",
    description: "Exclusive Yachtsport ",
  },
  {
    id: "colectia-Bmw-Golfsport",
    name: "Bmw Golfsport",
    href: "/Colectia-Bmw-Golfsport",
    image:
      "https://images.unsplash.com/photo-1508685096489-7aacd43bd3b1?w=200&h=200&fit=crop",
    description: "Bmw  Golfsport",
  },
  {
    id: "colectia-Bmw-Classic",
    name: "Bmw Classic",
    href: "/Colectia-Bmw-Classic",
    image:
      "https://images.unsplash.com/photo-1546868871-7041f2a55e12?w=200&h=200&fit=crop",
    description: "Classic designs",
  },
  {
    id: "colectia-Bmw-F",
    name: "Bmw F",
    href: "/Colectia-Bmw-F",
    image:
      "https://images.unsplash.com/photo-1614164185128-e4ec99c436d7?w=200&h=200&fit=crop",
    description: "Exclusive for enthusiasts",
  },
  {
    id: "colectia-Motociclete-Bmw",
    name: "Motociclete",
    href: "/Colectia-Motociclete-Bmw",
    image:
      "https://images.unsplash.com/photo-1508685096489-7aacd43bd3b1?w=200&h=200&fit=crop",
    description: "Motociclete Bmw",
  },
];

const wheelsAndTiresCategories: SubCategory[] = [
  {
    id: "roti-complete-vara",
    name: "Roti Complete Vara",
    href: "/Roti-Complete-Vara",
    icon: <Cylinder className="w-8 h-8" />,
    description: "Complete summer wheel sets for your BMW",
    color: "text-red-500",
  },
  {
    id: "jante",
    name: "Jante",
    href: "/Jante",
    icon: <Disc className="w-8 h-8" />,
    description: "Premium BMW alloy wheels",
    color: "text-blue-500",
  },
  {
    id: "accesorii-roti",
    name: "Accesorii Roti",
    href: "/Accesorii-Roti",
    icon: <Wrench className="w-8 h-8" />,
    description: "Wheel accessories and components",
    color: "text-green-500",
  },
  {
    id: "anvelope-vara",
    name: "Anvelope Vara",
    href: "/Anvelope-Vara",
    icon: <Cog className="w-8 h-8" />,
    description: "High-performance summer tires",
    color: "text-yellow-500",
  },
  {
    id: "roti-complete-iarna",
    name: "Roti Complete Iarna",
    href: "/Roti-Complete-Iarna",
    icon: <Cylinder className="w-8 h-8" />,
    description: "Complete winter wheel sets",
    color: "text-amber-500",
  },
  {
    id: "anvelope-iarna",
    name: "Anvelope Iarna",
    href: "/Anvelope-Iarna",
    icon: <Cog className="w-8 h-8" />,
    description: "Winter tires for optimal traction",
    color: "text-purple-500",
  },
  {
    id: "anvelope-all-season",
    name: "Anvelope All Season",
    href: "/Anvelope-All-Season",
    icon: <Cog className="w-8 h-8" />,
    description: "All-season tires for year-round performance",
    color: "text-cyan-500",
  },
];

const accesoriiInteriorCategories: CategoryAndSubcategory[] = [
  {
    label: "Protecție și siguranță",
    subcategories: [
      {
        id: "Set-Covorașe-Cauciuc",
        name: "Set Covorașe Cauciuc",
        href: "/Set-Covorașe-Cauciuc",
        image:
          "https://images.pexels.com/photos/414884/pexels-photo-414884.jpeg",
        description: "Protejează podeaua de murdărie și umezeală.",
      },
      {
        id: "Set-Covorașe-Textil",
        name: "Set Covorașe Textil",
        href: "/Set-Covorașe-Textil",
        image:
          "https://images.pexels.com/photos/193996/pexels-photo-193996.jpeg",
        description: "Oferă un aspect elegant și protecție interiorului.",
      },
      {
        id: "tava-covor-portbagaj",
        name: "Tavă/Covor Portbagaj",
        href: "/universale/organizare-intretinere/tava-covor-portbagaj",
        image: "https://images.pexels.com/photos/414884/pexels-photo-414884.jpeg",
        description: "Protecție durabilă pentru portbagaj împotriva murdăriei și lichidelor.",
      },
      {
        id: "Huse-Chei",
        name: "Huse Chei",
        href: "/Huse-Chei",
        image:
          "https://images.pexels.com/photos/2286873/pexels-photo-2286873.jpeg",
        description: "Protejează și personalizează cheia mașinii tale.",
      },
      {
        id: "Huse-Banchetă",
        name: "Huse Banchetă",
        href: "/Huse-Banchetă",
        image:
          "https://images.pexels.com/photos/193998/pexels-photo-193998.jpeg",
        description: "Protejează tapițeria și oferă un plus de confort.",
      },
      {
        id: "Scaun-Copil",
        name: "Scaun Copil",
        href: "/Scaun-Copil",
        image:
          "https://images.pexels.com/photos/853427/pexels-photo-853427.jpeg",
        description: "Asigură siguranța copilului în timpul călătoriei.",
      },
      {
        id: "Kit-Pană",
        name: "Kit Pană",
        href: "/Kit-Pana",
        image: "https://images.pexels.com/photos/193998/pexels-photo-193998.jpeg",
        description: "Soluții rapide pentru pene de cauciuc.",
      },
    ],
  },
  {
    label: "Organizare",
    subcategories: [
      {
        id: "Suport-Pahar",
        name: "Suport Pahar",
        href: "/Suport-Pahar",
        image:
          "https://images.pexels.com/photos/1899988/pexels-photo-1899988.jpeg",
        description: "Menține băuturile în siguranță în timpul condusului.",
      },
      {
        id: "Masă-Rabatabilă",
        name: "Masă Rabatabilă",
        href: "/Masă-Rabatabilă",
        image:
          "https://images.pexels.com/photos/1899988/pexels-photo-1899988.jpeg",
        description: "Oferă un suport practic pentru laptop sau gustări.",
      },
      {
        id: "Umeraș-Auto",
        name: "Umeraș Auto",
        href: "/Umeraș-Auto",
        image:
          "https://images.pexels.com/photos/1899988/pexels-photo-1899988.jpeg",
        description: "Ideal pentru a păstra hainele fără cute în mașină.",
      },
      {
        id: "Suport-Umbrelă",
        name: "Suport Umbrelă",
        href: "/Suport-Umbrelă",
        image:
          "https://images.pexels.com/photos/1134176/pexels-photo-1134176.jpeg",
        description: "Depozitează umbrela fără a murdări interiorul.",
      },
      {
        id: "Suport-Bază",
        name: "Suport Bază",
        href: "/Suport-Bază",
        image:
          "https://images.pexels.com/photos/97075/pexels-photo-97075.jpeg",
        description: "Un suport multifuncțional pentru diverse obiecte mici.",
      },
      {
        id: "Cârlig-Universal",
        name: "Cârlig Universal",
        href: "/Cârlig-Universal",
        image: "https://images.pexels.com/photos/853427/pexels-photo-853427.jpeg",
        description: "Compatibil cu diverse tipuri de remorci și accesorii.",
      },
      {
        id: "organizatoare-genti",
        name: "Organizatoare și Genți",
        href: "/universale/organizare-intretinere/organizatoare-genti",
        image: "https://images.pexels.com/photos/853427/pexels-photo-853427.jpeg",
        description: "Soluții practice pentru organizarea eficientă a spațiului din mașină.",
      },
      {
        id: "compartiment-depozitare",
        name: "Compartiment Depozitare",
        href: "/universale/organizare-intretinere/compartiment-depozitare",
        image: "https://images.pexels.com/photos/97075/pexels-photo-97075.jpeg",
        description: "Spații suplimentare de depozitare pentru o organizare perfectă.",
      },
      {
        id: "plasa-bagaje",
        name: "Plasă de Bagaje",
        href: "/universale/organizare-intretinere/plasa-bagaje",
        image: "https://images.pexels.com/photos/1899988/pexels-photo-1899988.jpeg",
        description: "Plasă elastică pentru fixarea bagajelor în siguranță.",
      },
    ],
  },
  {
    label: "Personalizare",
    subcategories: [
      {
        id: "Elemente-Decorative-Interior",
        name: "Elemente Decorative Interior",
        href: "/Elemente-Decorative-Interior",
        image:
          "https://images.pexels.com/photos/97075/pexels-photo-97075.jpeg",
        description: "Adaugă stil și personalitate interiorului mașinii.",
      },
      {
        id: "Pedale-M",
        name: "Pedale M",
        href: "/Pedale-M",
        image:
          "https://images.pexels.com/photos/707046/pexels-photo-707046.jpeg",
        description: "Pedale sport pentru un aspect și control îmbunătățit.",
      },
      {
        id: "Capac-Volan-M",
        name: "Capac Volan M",
        href: "/Capac-Volan-M",
        image:
          "https://images.pexels.com/photos/97075/pexels-photo-97075.jpeg",
        description: "Un capac sport pentru un plus de confort și aderență.",
      },
      {
        id: "Maner-Schimbător",
        name: "Maner Schimbător",
        href: "/Maner-Schimbător",
        image:
          "https://images.pexels.com/photos/193999/pexels-photo-193999.jpeg",
        description: "Schimbător ergonomic cu design modern.",
      },
      {
        id: "Maner-Frână-de-Mână",
        name: "Maner Frână de Mână",
        href: "/Maner-Frână-de-Mână",
        image:
          "https://images.pexels.com/photos/244205/pexels-photo-244205.jpeg",
        description: "Înlocuiește sau personalizează manerul frânei de mână.",
      },
      {
        id: "Capac-Oglindă-Interioară",
        name: "Capac Oglindă Interioară",
        href: "/Capac-Oglindă-Interioară",
        image:
          "https://images.pexels.com/photos/248747/pexels-photo-248747.jpeg",
        description: "Un detaliu elegant pentru oglinda retrovizoare.",
      },
    ],
  },
];

const accesoriiExteriorCategories: CategoryAndSubcategory[] = [
  {
    label: "Transport și remorcare",
    subcategories: [
      {
        id: "Suporturi-Cutii-Plafon",
        name: "Suporturi și Cutii Plafon",
        href: "/Suporturi-Cutii-Plafon",
        image: "https://images.pexels.com/photos/2286873/pexels-photo-2286873.jpeg",
        description: "Spațiu suplimentar pentru bagaje și echipamente.",
      },
      {
        id: "Suporturi-Transport-Spate",
        name: "Suporturi Transport Spate",
        href: "/Suporturi-Transport-Spate",
        image: "https://images.pexels.com/photos/1899988/pexels-photo-1899988.jpeg",
        description: "Transport eficient pentru biciclete sau alte echipamente.",
      },
      {
        id: "Bare-Transversale",
        name: "Bare Transversale",
        href: "/Bare-Transversale",
        image: "https://images.pexels.com/photos/853427/pexels-photo-853427.jpeg",
        description: "Suporturi ideale pentru transportul diverselor obiecte.",
      },
      {
        id: "Bare-Longitudinale",
        name: "Bare Longitudinale",
        href: "/Bare-Longitudinale",
        image: "https://images.pexels.com/photos/193998/pexels-photo-193998.jpeg",
        description: "Fixare solidă pentru transport sigur pe plafon.",
      },
      {
        id: "Cârlig-Remorcare",
        name: "Cârlig Remorcare",
        href: "/Cârlig-Remorcare",
        image: "https://images.pexels.com/photos/193996/pexels-photo-193996.jpeg",
        description: "Echipament robust pentru tractarea remorcilor.",
      },
      {
        id: "Accesorii-Transport-Spate",
        name: "Accesorii Transport Spate",
        href: "/Accesorii-Transport-Spate",
        image: "https://images.pexels.com/photos/193996/pexels-photo-193996.jpeg",
        description: "Accesorii suplimentare pentru transportul sigur.",
      },

    ],
  },
  {
    label: "Personalizare",
    subcategories: [
      {
        id: "Embleme-Exterior",
        name: "Embleme Exterior",
        href: "/Embleme-Exterior",
        image: "https://images.pexels.com/photos/248747/pexels-photo-248747.jpeg",
        description: "Personalizează aspectul mașinii cu embleme de calitate.",
      },
      {
        id: "Benzi-Decorative-Exterior",
        name: "Benzi Decorative Exterior",
        href: "/Benzi-Decorative-Exterior",
        image: "https://images.pexels.com/photos/193999/pexels-photo-193999.jpeg",
        description: "Adaugă un look sportiv și modern caroseriei.",
      },
      {
        id: "Stickere-Exterior",
        name: "Stickere Exterior",
        href: "/Stickere-Exterior",
        image: "https://images.pexels.com/photos/707046/pexels-photo-707046.jpeg",
        description: "Decorează mașina cu stickere de calitate superioară.",
      },
      {
        id: "Ornamente-Exterior",
        name: "Ornamente Exterior",
        href: "/Ornamente-Exterior",
        image: "https://images.pexels.com/photos/193999/pexels-photo-193999.jpeg",
        description: "Ornamente premium pentru un design elegant.",
      },
      {
        id: "Capac-Oglindă-Exterior",
        name: "Capac Oglindă Exterior",
        href: "/Capac-Oglindă-Exterior",
        image: "https://images.pexels.com/photos/97075/pexels-photo-97075.jpeg",
        description: "Protejează și personalizează oglinzile exterioare.",
      },
      {
        id: "Ornament-Praguri",
        name: "Ornament Praguri",
        href: "/Ornament-Praguri",
        image: "https://images.pexels.com/photos/414884/pexels-photo-414884.jpeg",
        description: "Accesorii pentru protecția și estetica pragurilor.",
      },
      {
        id: "Ornament-Tobă",
        name: "Ornament Tobă",
        href: "/Ornament-Tobă",
        image: "https://images.pexels.com/photos/244205/pexels-photo-244205.jpeg",
        description: "Adaugă un aspect sportiv sistemului de evacuare.",
      },
    ],
  },
];

const accesoriiUniversaleCategories: CategoryAndSubcategory[] = [
  {
    label: "Întreținere și curățare",
    subcategories: [
      {
        id: "adezivi-materiale-etansare",
        name: "Adezivi și Materiale de Etanșare",
        href: "/universale/organizare-intretinere/adezivi-materiale-etansare",
        image: "https://images.unsplash.com/photo-1598628461951-1448ad74f991?w=200&h=200&fit=crop",
        description: "Produse profesionale pentru reparații și etanșare durabilă.",
      },
      {
        id: "intretinere-igienizare",
        name: "Întreținere și Igienizare",
        href: "/universale/organizare-intretinere/intretinere-igienizare",
        image: "https://images.unsplash.com/photo-1617202022473-7203a1c9cd08?w=200&h=200&fit=crop",
        description: "Produse pentru curățenie și întreținere auto de calitate.",
      },
      {
        id: "Detergenți",
        name: "Detergenți",
        href: "/Detergenți",
        image: "https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=200&h=200&fit=crop",
        description: "Produse pentru curățarea eficientă a mașinii.",
      },
      {
        id: "Detailing",
        name: "Detailing",
        href: "/Detailing",
        image: "https://images.unsplash.com/photo-1615869286219-919c0b46e96c?w=200&h=200&fit=crop",
        description: "Soluții profesionale pentru îngrijirea auto.",
      },
      {
        id: "Odorizante",
        name: "Odorizante",
        href: "/Odorizante",
        image: "https://images.unsplash.com/photo-1555685812-4b943f1cb0eb?w=200&h=200&fit=crop",
        description: "Odorizante pentru un miros plăcut în mașină.",
      },
      {
        id: "Solutii-Intretinere-BMW",
        name: "Soluții Întreținere BMW",
        href: "/Solutii-Intretinere-BMW",
        image: "https://images.unsplash.com/photo-1518166171148-7e096a93e2a0?w=200&h=200&fit=crop",
        description: "Produse originale pentru întreținerea BMW.",
      },
      {
        id: "Solutii-Intretinere-MINI",
        name: "Soluții Întreținere MINI",
        href: "/Solutii-Intretinere-MINI",
        image: "https://images.unsplash.com/photo-1513797311991-1c90eb1767c2?w=200&h=200&fit=crop",
        description: "Produse originale pentru întreținerea MINI.",
      },
      {
        id: "Reconditionare-Piele",
        name: "Recondiționare Piele",
        href: "/Reconditionare-Piele",
        image: "https://images.unsplash.com/photo-1622171574976-778d6c654b52?w=200&h=200&fit=crop",
        description: "Soluții pentru întreținerea și restaurarea pielii.",
      },
    ],
  },
  {
    label: "Întreținere tehnică",
    subcategories: [
      {
        id: "Aditivi-Motor",
        name: "Aditivi Motor",
        href: "/Aditivi-Motor",
        image: "https://images.unsplash.com/photo-1591767921627-321078776f16?w=200&h=200&fit=crop",
        description: "Aditivi pentru performanța și protecția motorului.",
      },
      {
        id: "Grasimi-si-Paste",
        name: "Grăsimi și Paste",
        href: "/Grasimi-si-Paste",
        image: "https://images.unsplash.com/photo-1580910048965-c4f77764b3fa?w=200&h=200&fit=crop",
        description: "Lubrifianți pentru protecție și durabilitate.",
      },
      {
        id: "Protectie-Impotriva-Coroziunii",
        name: "Protecție Împotriva Coroziunii",
        href: "/Protectie-Impotriva-Coroziunii",
        image: "https://images.unsplash.com/photo-1591733669404-766d732f2289?w=200&h=200&fit=crop",
        description: "Produse pentru protecția împotriva ruginii.",
      },
      {
        id: "Solutie-Lipire-Suruburi",
        name: "Soluție Lipire Șuruburi",
        href: "/Solutie-Lipire-Suruburi",
        image: "https://images.unsplash.com/photo-1580910048965-c4f77764b3fa?w=200&h=200&fit=crop",
        description: "Fixare sigură a șuruburilor și piulițelor.",
      },
      {
        id: "Sprayuri-Multifunctionale",
        name: "Sprayuri Multifuncționale",
        href: "/Sprayuri-Multifunctionale",
        image: "https://images.unsplash.com/photo-1597318186593-6779f9c421e6?w=200&h=200&fit=crop",
        description: "Lubrifiere și protecție universală.",
      },
    ],
  },
  {
    label: "Electrica",
    subcategories: [
      {
        id: "baterii-auto",
        name: "Baterii Auto",
        href: "/universale/electrice/baterii-auto",
        image: "https://images.unsplash.com/photo-1602541918053-fb54f30a8933?w=200&h=200&fit=crop",
        description: "Baterii auto de înaltă performanță pentru toate modelele.",
      },
      {
        id: "baterii-telecomanda",
        name: "Baterii Telecomandă",
        href: "/universale/electrice/baterii-telecomanda",
        image: "https://images.unsplash.com/photo-1624378435121-fd09248db7eb?w=200&h=200&fit=crop",
        description: "Baterii de schimb pentru telecomenzile BMW și MINI.",
      },
      {
        id: "cablu-incarcare-masini-electrice",
        name: "Cablu Încărcare Mașini Electrice",
        href: "/universale/electrice/cablu-incarcare-masini-electrice",
        image: "https://images.unsplash.com/photo-1626614064010-6dfc69a4d336?w=200&h=200&fit=crop",
        description: "Cabluri de încărcare rapide și sigure pentru mașini electrice.",
      },
      {
        id: "wallbox",
        name: "Wallbox",
        href: "/universale/electrice/wallbox",
        image: "https://images.unsplash.com/photo-1615873968403-3d08136dcf96?w=200&h=200&fit=crop",
        description: "Stații de încărcare Wallbox pentru uz casnic și profesional.",
      },
    ]
  }
  // {
  //   label: "Siguranță și urgențe",
  //   subcategories: [
  //     {
  //       id: "Siguranța-Auto",
  //       name: "Siguranța Auto",
  //       href: "/Siguranta-Auto",
  //       image: "https://images.unsplash.com/photo-1526726538690-5cbf956ae2fd?w=200&h=200&fit=crop",
  //       description: "Accesorii pentru siguranța rutieră.",
  //     },

  //   ],
  // },
];

// const consumabileCategories: SubCategory[] = [
//   {
//     id: "filtru-aer",
//     name: "Filtru Aer",
//     href: "/Filtru-Aer",
//     icon: <Wind className="w-8 h-8" />,
//     description: "Filtre de aer pentru motorul tău BMW",
//     color: "text-blue-500",
//   },
//   {
//     id: "filtru-ulei",
//     name: "Filtru Ulei",
//     href: "/Filtru-Ulei",
//     icon: <Droplets className="w-8 h-8" />,
//     description: "Filtre de ulei de înaltă calitate",
//     color: "text-yellow-500",
//   },
//   {
//     id: "filtru-combustibil",
//     name: "Filtru Combustibil",
//     href: "/Filtru-Combustibil",
//     icon: <Filter className="w-8 h-8" />,
//     description: "Filtre de combustibil pentru performanță optimă",
//     color: "text-red-500",
//   },
//   {
//     id: "kit-uri-service",
//     name: "Kit Uri Service",
//     href: "/Kit-Uri-Service",
//     icon: <Package className="w-8 h-8" />,
//     description: "Kituri complete pentru service",
//     color: "text-green-500",
//   },
//   {
//     id: "bujii",
//     name: "Bujii",
//     href: "/Bujii",
//     icon: <Sparkles className="w-8 h-8" />,
//     description: "Bujii pentru aprindere perfectă",
//     color: "text-orange-500",
//   },
//   {
//     id: "pompa-apa",
//     name: "Pompa Apa",
//     href: "/Pompa-Apa",
//     icon: <Droplet className="w-8 h-8" />,
//     description: "Pompe de apă pentru sistemul de răcire",
//     color: "text-cyan-500",
//   },
//   {
//     id: "ambreiaj",
//     name: "Ambreiaj",
//     href: "/Ambreiaj",
//     icon: <Disc className="w-8 h-8" />,
//     description: "Sisteme de ambreiaj de înaltă performanță",
//     color: "text-purple-500",
//   },
//   {
//     id: "piese-de-uzura-diverse",
//     name: "Piese De Uzura Diverse",
//     href: "/Piese-De-Uzura-Diverse",
//     icon: <Wrench className="w-8 h-8" />,
//     description: "Diverse piese de uzură pentru întreținere",
//     color: "text-gray-500",
//   },
//   {
//     id: "amortizor",
//     name: "Amortizor",
//     href: "/Amortizor",
//     icon: <Waves className="w-8 h-8" />,
//     description: "Amortizoare pentru confort și performanță",
//     color: "text-indigo-500",
//   },
//   {
//     id: "senzori-uzura",
//     name: "Senzori Uzura",
//     href: "/Senzori-Uzura",
//     icon: <Gauge className="w-8 h-8" />,
//     description: "Senzori pentru monitorizarea uzurii",
//     color: "text-blue-600",
//   },
//   {
//     id: "placute-frana",
//     name: "Placute Frana",
//     href: "/Placute-Frana",
//     icon: <Hammer className="w-8 h-8" />,
//     description: "Plăcuțe de frână pentru oprire sigură",
//     color: "text-red-600",
//   },
//   {
//     id: "discuri-frana",
//     name: "Discuri Frana",
//     href: "/Discuri-Frana",
//     icon: <Disc className="w-8 h-8" />,
//     description: "Discuri de frână de înaltă performanță",
//     color: "text-gray-600",
//   },
//   {
//     id: "stergatoare",
//     name: "Stergatoare",
//     href: "/stergatoare",
//     icon: <Waves className="w-8 h-8" />,
//     description: "Ștergătoare pentru vizibilitate perfectă",
//     color: "text-blue-400",
//   },
//   {
//     id: "curea-ventilator",
//     name: "Curea Ventilator",
//     href: "/Curea-Ventilator",
//     icon: <Fan className="w-8 h-8" />,
//     description: "Curele pentru ventilator",
//     color: "text-green-600",
//   },
//   {
//     id: "ventilator-si-curea",
//     name: "Ventilator si Curea",
//     href: "/Ventilator-si-Curea",
//     icon: <Fan className="w-8 h-8" />,
//     description: "Seturi complete de ventilator și curea",
//     color: "text-teal-500",
//   },
//   {
//     id: "supape-garnituri",
//     name: "Supape Garnituri",
//     href: "/Supape-Garnituri",
//     icon: <Cylinder className="w-8 h-8" />,
//     description: "Supape și garnituri pentru motor",
//     color: "text-amber-500",
//   },
//   {
//     id: "bucsi",
//     name: "Bucsi",
//     href: "/Bucsi",
//     icon: <Cylinder className="w-8 h-8" />,
//     description: "Bucși pentru suspensie și direcție",
//     color: "text-yellow-600",
//   },
//   {
//     id: "rulment-si-butuc",
//     name: "Rulment si Butuc",
//     href: "/Rulment-si-Butuc",
//     icon: <Disc className="w-8 h-8" />,
//     description: "Rulmenți și butuci pentru roți",
//     color: "text-orange-600",
//   },
//   {
//     id: "amortizoare",
//     name: "Amortizoare",
//     href: "/Amortizoare",
//     icon: <Waves className="w-8 h-8" />,
//     description: "Amortizoare pentru toate modelele BMW",
//     color: "text-violet-500",
//   },
//   {
//     id: "roti-si-anvelope",
//     name: "Roti si Anvelope",
//     href: "/Roti-si-Anvelope",
//     icon: <Car className="w-8 h-8" />,
//     description: "Roți și anvelope complete",
//     color: "text-slate-600",
//   },
// ];


const consumabileCategories: SubCategory[] = [
  {
    id: "filtru-ulei",
    name: "Filtru Ulei",
    href: "/consumabile/filtru-ulei",
    icon: <Filter className="" />,
    color: "text-yellow-500",
  },
  {
    id: "filtru-aer",
    name: "Filtru Aer",
    href: "/consumabile/filtru-aer",
    icon: <Filter className="" />,
    color: "text-blue-500",
  },
  {
    id: "filtru-habitaclu",
    name: "Filtru Habitaclu",
    href: "/consumabile/filtru-habitaclu",
    icon: <Filter className="" />,
    color: "text-green-500",
  },
  {
    id: "filtru-aer-conditionat",
    name: "Filtru Aer Condiționat",
    href: "/consumabile/filtru-aer-conditionat",
    icon: <Filter className="" />,
    color: "text-teal-500",
  },
  {
    id: "filtru-combustibil",
    name: "Filtru Combustibil",
    href: "/consumabile/filtru-combustibil",
    icon: <Filter className="" />,
    color: "text-orange-500",
  },
  {
    id: "disc-frana",
    name: "Disc Frână",
    href: "/consumabile/disc-frana",
    icon: <Disc className="" />,
    color: "text-gray-500",
  },
  {
    id: "set-placute-frana",
    name: "Set Plăcuțe Frână",
    href: "/consumabile/set-placute-frana",
    icon: <Disc className="" />,
    color: "text-red-500",
  },
  {
    id: "senzor-frana",
    name: "Senzor Frână",
    href: "/consumabile/senzor-frana",
    icon: <Wrench className="" />,
    color: "text-purple-500",
  },
  {
    id: "bujii",
    name: "Bujii",
    href: "/consumabile/bujii",
    icon: <Plug className="" />,
    color: "text-cyan-500",
  },
  {
    id: "ulei-motor",
    name: "Ulei Motor",
    href: "/consumabile/ulei-motor",
    icon: <Amphora className="" />,
    color: "text-amber-500",
  },
  {
    id: "ulei-cutie-viteze",
    name: "Ulei Cutie Viteze",
    href: "/consumabile/ulei-cutie-viteze",
    icon: <Amphora className="" />,
    color: "text-indigo-500",
  },
  {
    id: "ulei-cutie-transfer",
    name: "Ulei Cutie Transfer",
    href: "/consumabile/ulei-cutie-transfer",
    icon: <Amphora className="" />,
    color: "text-lime-500",
  },
  {
    id: "lichid-frana",
    name: "Lichid de Frână",
    href: "/consumabile/lichid-frana",
    icon: <Droplet className="" />,
    color: "text-fuchsia-500",
  },
  {
    id: "antigel",
    name: "Antigel",
    href: "/consumabile/antigel",
    icon: <Droplet className="" />,
    color: "text-emerald-500",
  },
  {
    id: "adblue",
    name: "AdBlue",
    href: "/consumabile/adblue",
    icon: <Droplet className="" />,
    color: "text-rose-500",
  },
  {
    id: "lichid-parbriz",
    name: "Lichid Parbriz",
    href: "/consumabile/lichid-parbriz",
    icon: <Droplet className="" />,
    color: "text-violet-500",
  },
  {
    id: "stergatoare",
    name: "Ștergătoare",
    href: "/consumabile/stergatoare",
    icon: <Wind className="" />,
    color: "text-purple-500",
  },
];

const pieseCategories: CategoryAndSubcategory[] = [
  {
    label: "Caroserie",
    subcategories: [
      { id: "oglinda-usa", name: "Oglinda Usa", href: "/Oglinda-Usa" },
      { id: "piese-ghidaj", name: "Piese Ghidaj", href: "/Piese-Ghidaj" },
      { id: "diverse-piese-usi", name: "Diverse Piese Usi", href: "/Diverse-Piese-Usi"},
      { id: "radiator-ulei", name: "Radiator Ulei", href: "/Radiator-Ulei" },
      { id: "chedere-usi", name: "Chedere Usi", href: "/Chedere-Usi" },
      { id: "diverse-piese", name: "Diverse Piese", href: "/Diverse-Piese" },
      { id: "caroserie", name: "Caroserie", href: "/Caroserie" },
      { id: "grile", name: "Grile", href: "/Grile" },
      { id: "parbriz", name: "Parbriz", href: "/Parbriz" },
      { id: "embleme", name: "Embleme", href: "/Embleme" },
      { id: "ornamente-caroserie", name: "Ornamente Caroserie", href: "/Ornamente-Caroserie"},
      { id: "capac-fata", name: "Capac Fata", href: "/Capac-Fata" },
      { id: "ornamente-si-chedere", name: "Ornamente si Chedere", href: "/Ornamente-si-Chedere", },
      { id: "absorbant-soc-fata", name: "Absorbant soc Fata", href: "/Absorbant-soc-Fata", },
      { id: "airbag", name: "Airbag", href: "/Airbag" },
      { id: "radiator", name: "Radiator", href: "/Radiator" },
      { id: "capac-spate", name: "Capac Spate", href: "/Capac-Spate" },
      { id: "lumini-spate", name: "Lumini Spate", href: "/Lumini-Spate" },
      {
        id: "absorbant-soc-spate",
        name: "Absorbant soc Spate",
        href: "/Absorbant-soc-Spate",
      },
      {
        id: "diverse-piese-radiator",
        name: "Diverse Piese Radiator",
        href: "/Diverse-Piese-Radiator",
      },
      { id: "accidente", name: "Accidente", href: "/Accidente" },
      { id: "baie-ulei", name: "Baie Ulei", href: "/Baie-Ulei" },
      { id: "curele", name: "Curele", href: "/Curele" },
      { id: "faruri", name: "Faruri", href: "/Faruri" },
      { id: "aripa-fata", name: "Aripa Fata", href: "/Aripa-Fata" },
      { id: "semnalizatoare", name: "Semnalizatoare", href: "/Semnalizatoare" },
      { id: "usa-spate", name: "Usa Spate", href: "/Usa-Spate" },
      { id: "aripa-spate", name: "Aripa Spate", href: "/Aripa-Spate" },
      {
        id: "panouri-fata-spate",
        name: "Panouri Fata Spate",
        href: "/Panouri-Fata-Spate",
      },
      { id: "rezervor", name: "Rezervor", href: "/Rezervor" },
      { id: "lumini-ceata", name: "Lumini Ceata", href: "/Lumini-Ceata" },
      { id: "lumini-frana", name: "Lumini Frana", href: "/Lumini-Frana" },
      {
        id: "ventilator-si-cuplaj",
        name: "Ventilator si Cuplaj",
        href: "/Ventilator-si-Cuplaj",
      },
      { id: "usa-fata", name: "Usa Fata", href: "/Usa-Fata" },
      {
        id: "ornamente-cauciuc",
        name: "Ornamente Cauciuc",
        href: "/Ornamente-Cauciuc",
      },
    ],
  },
  {
    label: "Generale",
    subcategories: [
      { id: "panou-interior", name: "Panou Interior", href: "/Panou-Interior" },
      { id: "fata-de-usa", name: "Fata De Usa", href: "/Fata-De-Usa" },
      { id: "motor", name: "Motor", href: "/Motor" },
      { id: "chiulasa", name: "Chiulasa", href: "/Chiulasa" },
      { id: "izolatii", name: "Izolatii", href: "/Izolatii" },
      {
        id: "echipament-interior",
        name: "Echipament Interior",
        href: "/Echipament-Interior",
      },
      { id: "conducte", name: "Conducte", href: "/Conducte" },
      {
        id: "galerie-admisie",
        name: "Galerie Admisie",
        href: "/Galerie-Admisie",
      },
      {
        id: "suspensie-motor",
        name: "Suspensie Motor",
        href: "/Suspensie-Motor",
      },
      {
        id: "articole-diverse-reparare",
        name: "Articole Diverse Reparare",
        href: "/Articole-Diverse-Reparare",
      },
      { id: "trapa-hard-top", name: "Trapa Hard Top", href: "/Trapa-Hard-Top" },
      { id: "climatizare", name: "Climatizare", href: "/Climatizare" },
      { id: "arc", name: "Arc", href: "/Arc" },
      {
        id: "schimbator-viteze",
        name: "Schimbator Viteze",
        href: "/Schimbator-Viteze",
      },
      { id: "scaune-fata", name: "Scaune Fata", href: "/Scaune-Fata" },
      { id: "injectie", name: "Injectie", href: "/Injectie" },
      { id: "reparatii", name: "Reparatii", href: "/Reparatii" },
      { id: "unitate-abs", name: "Unitate Abs", href: "/Unitate-Abs" },
      { id: "garnituri", name: "Garnituri", href: "/Garnituri" },
      {
        id: "incuietoare-usa",
        name: "Incuietoare Usa",
        href: "/Incuietoare-Usa",
      },
      {
        id: "echipament-motor-si-sasiu",
        name: "Echipament Motor si sasiu",
        href: "/Echipament-Motor-si-sasiu",
      },
      {
        id: "cutie-de-viteze",
        name: "Cutie De Viteze",
        href: "/Cutie-De-Viteze",
      },
      {
        id: "piese-diverse-motor",
        name: "Piese Diverse Motor",
        href: "/Piese-Diverse-Motor",
      },
      {
        id: "set-reparatie-directie",
        name: "Set Reparatie Directie",
        href: "/Set-Reparatie-Directie",
      },
      {
        id: "mecanism-angrenare",
        name: "Mecanism Angrenare",
        href: "/Mecanism-Angrenare",
      },
      { id: "cutie-automata", name: "Cutie Automata", href: "/Cutie-Automata" },
      { id: "evacuare", name: "Evacuare", href: "/Evacuare" },
      { id: "kit-reparatie", name: "Kit Reparatie", href: "/Kit-Reparatie" },
      { id: "lubrifiere", name: "Lubrifiere", href: "/Lubrifiere" },
      { id: "planetare", name: "Planetare", href: "/Planetare" },
      {
        id: "recirculare-gaze",
        name: "Recirculare Gaze",
        href: "/Recirculare-Gaze",
      },
      {
        id: "controlul-supapelor",
        name: "Controlul Supapelor",
        href: "/Controlul-Supapelor",
      },
      { id: "scaune-spate", name: "Scaune Spate", href: "/Scaune-Spate" },
      { id: "cutie-manuala", name: "Cutie Manuala", href: "/Cutie-Manuala" },
      { id: "punte-fata", name: "Punte Fata", href: "/Punte-Fata" },
      { id: "parasolar", name: "Parasolar", href: "/Parasolar" },
      { id: "punte-spate", name: "Punte Spate", href: "/Punte-Spate" },
      {
        id: "pompa-servodirectie",
        name: "Pompa Servodirectie",
        href: "/Pompa-Servodirectie",
      },
      { id: "turbocompresor", name: "Turbocompresor", href: "/Turbocompresor" },
      {
        id: "diverse-piese-sistem-franare",
        name: "Diverse Piese Sistem Franare",
        href: "/Diverse-Piese-Sistem-Franare",
      },
      { id: "bascule", name: "Bascule", href: "/Bascule" },
      {
        id: "cutie-depozitare",
        name: "Cutie Depozitare",
        href: "/Cutie-Depozitare",
      },
      {
        id: "caseta-directie",
        name: "Caseta Directie",
        href: "/Caseta-Directie",
      },
      { id: "curea", name: "Curea", href: "/Curea" },
      {
        id: "accesorii-roti-si-anvelope",
        name: "Accesorii Roti si Anvelope",
        href: "/Accesorii-Roti-si-Anvelope",
      },
      { id: "cotiera", name: "Cotiera", href: "/Cotiera" },
      { id: "supape", name: "Supape", href: "/Supape" },
      { id: "termostat", name: "Termostat", href: "/Termostat" },
      { id: "piston", name: "Piston", href: "/Piston" },
      { id: "stabilizator", name: "Stabilizator", href: "/Stabilizator" },
      { id: "piese-standard", name: "Piese Standard", href: "/Piese-Standard" },
      {
        id: "montant-punte-spate",
        name: "Montant Punte Spate",
        href: "/Montant-Punte-Spate",
      },
      { id: "catalizator", name: "Catalizator", href: "/Catalizator" },
      { id: "torpedou", name: "Torpedou", href: "/Torpedou" },
      { id: "bielete", name: "Bielete", href: "/Bielete" },
      {
        id: "garnitura-chiulasa",
        name: "Garnitura Chiulasa",
        href: "/Garnitura-Chiulasa",
      },
      {
        id: "galerie-evacuare",
        name: "Galerie Evacuare",
        href: "/Galerie-Evacuare",
      },
      {
        id: "alte-module-control",
        name: "Alte Module Control",
        href: "/Alte-Module-Control",
      },
      { id: "carter", name: "Carter", href: "/Carter" },
      {
        id: "pregatire-amestec",
        name: "Pregatire Amestec",
        href: "/Pregatire-Amestec",
      },
      { id: "debitmetru", name: "Debitmetru", href: "/Debitmetru" },
      {
        id: "bujii-incandescente",
        name: "Bujii Incandescente",
        href: "/Bujii-Incandescente",
      },
      { id: "huse-scaun", name: "Huse Scaun", href: "/Huse-Scaun" },
      { id: "manson-cauciuc", name: "Manson Cauciuc", href: "/Manson-Cauciuc" },
    ],
  },
  {
    label: "Electronice",
    subcategories: [
      { id: "cablaje", name: "Cablaje", href: "/Cablaje" },
      { id: "hifi", name: "Hifi", href: "/Hifi" },
      { id: "comutatoare", name: "Comutatoare", href: "/Comutatoare" },
      {
        id: "diverse-piese-electrice",
        name: "Diverse Piese Electrice",
        href: "/Diverse-Piese-Electrice",
      },
      {
        id: "cablaje-aprindere",
        name: "Cablaje Aprindere",
        href: "/Cablaje-Aprindere",
      },
      { id: "aprindere", name: "Aprindere", href: "/Aprindere" },
      { id: "alternator", name: "Alternator", href: "/Alternator" },
      { id: "electromotor", name: "Electromotor", href: "/Electromotor" },
      {
        id: "control-climatizare",
        name: "Control Climatizare",
        href: "/Control-Climatizare",
      },
      {
        id: "comunicatii-mobile",
        name: "Comunicatii Mobile",
        href: "/Comunicatii-Mobile",
      },
      {
        id: "modul-motor-electric",
        name: "Modul Motor Electric",
        href: "/Modul-Motor-Electric",
      },
      { id: "display-uri", name: "Display Uri", href: "/Display-Uri" },
      { id: "senzori-abs", name: "Senzori Abs", href: "/Senzori-Abs" },
      {
        id: "control-suspensie",
        name: "Control Suspensie",
        href: "/Control-Suspensie",
      },
      { id: "soclu-bec", name: "Soclu Bec", href: "/Soclu-Bec" },
      { id: "baterie", name: "Baterie", href: "/Baterie" },
      {
        id: "componente-electrice",
        name: "Componente Electrice",
        href: "/Componente-Electrice",
      },
      {
        id: "compentente-electrice-stergatoare",
        name: "Compentente stergatoare",
        href: "/Compentente-Electrice-stergatoare",
      },
      {
        id: "lumini-interioare",
        name: "Lumini Interioare",
        href: "/Lumini-Interioare",
      },
      { id: "cd-dvd-player", name: "Cd Dvd Player", href: "/Cd-Dvd-Player" },
      {
        id: "monitor-control",
        name: "Monitor Control",
        href: "/Monitor-Control",
      },
      { id: "sisteme-alarma", name: "Sisteme Alarma", href: "/Sisteme-Alarma" },
      {
        id: "calculator-far-xenon",
        name: "Calculator Far Xenon",
        href: "/Calculator-Far-Xenon",
      },
      { id: "instrumente", name: "Instrumente", href: "/Instrumente" },
      {
        id: "macarale-electrice",
        name: "Macarale Electrice",
        href: "/Macarale-Electrice",
      },
      { id: "radio", name: "Radio", href: "/Radio" },
      { id: "unitate-airbag", name: "Unitate Airbag", href: "/Unitate-Airbag" },
      {
        id: "rezistori-senzori",
        name: "Rezistori Senzori",
        href: "/Rezistori-Senzori",
      },
      { id: "releu-bujii", name: "Releu Bujii", href: "/Releu-Bujii" },
      { id: "navigatie", name: "Navigatie", href: "/Navigatie" },
      {
        id: "unitate-control-injectie",
        name: "Unitate Control Injectie",
        href: "/Unitate-Control-Injectie",
      },
      {
        id: "control-cutie-automata",
        name: "Control Cutie Automata",
        href: "/Control-Cutie-Automata",
      },
      {
        id: "computer-comunicare",
        name: "Computer Comunicare",
        href: "/Computer-Comunicare",
      },
      {
        id: "pompa-combustibil",
        name: "Pompa Combustibil",
        href: "/Pompa-Combustibil",
      },
      {
        id: "sisteme-electronice-blocare",
        name: "Sisteme Electronice Blocare",
        href: "/Sisteme-Electronice-Blocare",
      },
      { id: "senzori", name: "Senzori", href: "/Senzori" },
      { id: "computer-bord", name: "Computer Bord", href: "/Computer-Bord" },
      {
        id: "bobine-aprindere",
        name: "Bobine Aprindere",
        href: "/Bobine-Aprindere",
      },
      { id: "software", name: "Software", href: "/Software" },
      { id: "becuri", name: "Becuri", href: "/Becuri" },
    ],
  },
];

const MegaMenu: React.FC<MegaMenuProps> = ({
  isOpen = true,
  onClose = () => {},
  defaultTab,
}) => {
  const [activeTab, setActiveTab] = React.useState(defaultTab);

  React.useEffect(() => {
    if (defaultTab) {
      setActiveTab(defaultTab);
    }
  }, [defaultTab]);

  if (!isOpen) return null;

  const renderLifestyleContent = () => (
    <div className="grid grid-cols-7 gap-6">
      {lifestyleCategories.map((item) => (
        <a
          key={item.id}
          href={item.href}
          className="group flex flex-col items-center  rounded-lg transition-colors py-2"
        >
          <div className="h-24 w-24 mb-1 overflow-hidden rounded-lg">
          <Image
            src={item.image || "/placeholder-image.jpg"} // Add a placeholder image
            alt={item.name || "Product image"}
            width={200}
            height={150}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            sizes="(max-width: 768px) 100vw, 200px"
          />
          </div>
            <h3 className="font-medium text-base  mb-1">
              {item.name}
            </h3>
        </a>
      ))}
    </div>
  );

  const renderAccesoriiInteriorContent = () => (
    <div className="w-full">
      <Tabs defaultValue={accesoriiInteriorCategories[0].label}>
        <TabsList className="w-full border-b  mb-4 flex flex-wrap">
          {accesoriiInteriorCategories.map((category) => (
            <TabsTrigger
              key={category.label}
              value={category.label}
              className="px-4"
            >
              {category.label.replace(" Accesorii", "")}
            </TabsTrigger>
          ))}
        </TabsList>

        {accesoriiInteriorCategories.map((category) => (
          <TabsContent
            key={category.label}
            value={category.label}
            className="grid grid-cols-6 gap-6"
          >
            {category.subcategories.map((subcategory) => (
              <a
                key={subcategory.id}
                href={subcategory.href}
                className="group p-4  rounded-lg transition-colors border   hover:shadow-md"
              >
                <div className="h-24 mb-3 overflow-hidden rounded-lg  flex items-center justify-center">
                  <Image
                    src={subcategory.image || "/placeholder-image.jpg"} // Add a placeholder image
                    alt={subcategory.name || "Product image"}
                    width={200}
                    height={150}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    sizes="(max-width: 768px) 100vw, 200px"
                  />
                </div>
                <h3 className="font-semibold  mb-1">
                  {subcategory.name}
                </h3>
                <p className="text-sm ">
                  {subcategory.description?.toLowerCase()}
                </p>
              </a>
            ))}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );

  const renderAccesoriiExteriorContent = () => (
    <div className="w-full">
      <Tabs defaultValue={accesoriiExteriorCategories[0].label}>
        <TabsList className="w-full border-b border-gray-200 mb-2 flex flex-wrap">
          {accesoriiExteriorCategories.map((category) => (
            <TabsTrigger
              key={category.label}
              value={category.label}
              className="px-4"
            >
              {category.label.replace(" Accesorii", "")}
            </TabsTrigger>
          ))}
        </TabsList>

        {accesoriiExteriorCategories.map((category) => (
          <TabsContent
            key={category.label}
            value={category.label}
            className="grid grid-cols-7 gap-6"
          >
            {category.subcategories.map((subcategory) => (
              <a
                key={subcategory.id}
                href={subcategory.href}
                className="group p-4 rounded-lg transition-colors border border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 hover:shadow-md"
              >
                <div className="h-24 mb-3 overflow-hidden rounded-lg bg-gray-100 flex items-center justify-center">
                  <Image
                    src={subcategory.image || "/placeholder-image.jpg"} // Add a placeholder image
                    alt={subcategory.name || "Product image"}
                    width={200}
                    height={150}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    sizes="(max-width: 768px) 100vw, 200px"
                  />
                </div>
                <h3 className="font-semibold  mb-1">
                  {subcategory.name}
                </h3>
                <p className="text-sm ">
                  {subcategory.description?.toLowerCase()}
                </p>
              </a>
            ))}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );

  const renderUniversaleContent = () => (
    <div className="w-full">
      <Tabs defaultValue={accesoriiUniversaleCategories[0].label}>
        <TabsList className="w-full border-b border-gray-200 mb-4 flex flex-wrap">
          {accesoriiUniversaleCategories.map((category) => (
            <TabsTrigger
              key={category.label}
              value={category.label}
              className="px-4"
            >
              {category.label.replace(" Accesorii", "")}
            </TabsTrigger>
          ))}
        </TabsList>

        {accesoriiUniversaleCategories.map((category) => (
          <TabsContent
            key={category.label}
            value={category.label}
            className="grid grid-cols-6 gap-6"
          >
            {category.subcategories.map((subcategory) => (
              <a
                key={subcategory.href}
                href={subcategory.href}
                className="group p-4 rounded-lg transition-colors border border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 hover:shadow-md"
              >
                <div className="h-24 mb-3 overflow-hidden rounded-lg  flex items-center justify-center">
                  <img
                    src={subcategory.image}
                    alt={subcategory.name}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="font-semibold  mb-1">
                  {subcategory.name}
                </h3>
                <p className="text-sm ">
                  {subcategory.description?.toLowerCase()}
                </p>
              </a>
            ))}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );

  const renderWheelsAndTiresContent = () => (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {wheelsAndTiresCategories.map((item) => (
        <a
          key={item.id}
          href={item.href}
          className="group p-4 rounded-lg transition-colors border border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 hover:shadow-md"
        >
          <div
            className={`${item.color} mb-3 transform group-hover:scale-110 transition-transform duration-300`}
          >
            {item.icon}
          </div>
          <h3 className="font-semibold  mb-1">{item.name}</h3>
          <p className="text-sm ">{item.description}</p>
        </a>
      ))}
    </div>
  );

  const renderConsumabileContent = () => (
    <div className="grid grid-cols-5 gap-8">
      {consumabileCategories.map((item) => (
        <a
          key={item.id}
          href={item.href}
          className="p-2 rounded-md transition-colors border-b-2  hover:border-[#0066B1] font-medium flex items-center gap-2"
        >
          <div className={`${item.color} flex-shrink-0`}>
          {item.icon ? (
               React.cloneElement(item.icon, { className: "w-4 h-8" })
             ) : null}
          </div>
          <div>
          <span
           className=""
           >
            {item.name}
          </span>
          </div>
        </a>
      ))}
    </div>
  );

  const renderPieseContent = () => (
    <div className="w-full">
      <Tabs defaultValue={pieseCategories[0].label}>
        <TabsList className="w-full border-b  mb-4 flex flex-wrap">
          {pieseCategories.map((category) => (
            <TabsTrigger
              key={category.label}
              value={category.label}
              className="px-4"
            >
              {category.label.replace(" Piese", "")}
            </TabsTrigger>
          ))}
        </TabsList>

        {pieseCategories.map((category) => (
          <TabsContent
            key={category.label}
            value={category.label}
            className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-7 gap-2"
          >
            {category.subcategories.map((item) => (
              <a
                key={item.id}
                href={item.href}
                className="p-2 text-sm  rounded-md transition-colors border-b-2 border-[#E6E6E6] hover:border-[#0066B1] font-medium relative group"
              >
                {item.name}
              </a>
            ))}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.3 }}
    >
      <div className="p-4 md:p-6">
        <ScrollArea className="h-[320px] pr-4">
          {activeTab === "lifestyle" && renderLifestyleContent()}
          {activeTab === "wheels-tires" && renderWheelsAndTiresContent()}
          {activeTab === "accesorii-interior" && renderAccesoriiInteriorContent()}
          {activeTab === "accesorii-exterior" && renderAccesoriiExteriorContent()}
          {activeTab === "universale" && renderUniversaleContent()}
          {activeTab === "consumabile" && renderConsumabileContent()}
          {activeTab === "piese" && renderPieseContent()}
        </ScrollArea>
      </div>
    </motion.div>
  );
};

export default MegaMenu;


//Merge
// "use client"

// import React from "react";
// import { motion } from "framer-motion";
// import { ScrollArea } from "@/components/ui/scroll-area";
// import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
// import {
//   Disc,
//   Gauge,
//   Filter,
//   Zap,
//   Droplet,
//   Battery,
//   Fan,
//   Wrench,
//   Cog,
//   Cylinder,
//   Wind,
//   Droplets,
//   Flame,
//   Package,
//   Waves,
//   Sparkles,
//   Hammer,
//   Car,
// } from "lucide-react";

// interface SubCategory {
//   id: string;
//   name: string;
//   href: string;
//   icon?: React.ReactElement<{className?: string}>;
//   description?: string;
//   color?: string;
//   image?: string;
// }

// interface MegaMenuProps {
//   isOpen?: boolean;
//   onClose?: () => void;
//   defaultTab?: string;
// }

// const lifestyleCategories: SubCategory[] = [
//   {
//     id: "colectia-bmw-m",
//     name: "Colectia BMW M",
//     href: "/Colectia-Bmw-M",
//     image:
//       "https://images.unsplash.com/photo-1614164185128-e4ec99c436d7?w=200&h=200&fit=crop",
//     description: "For enthusiasts",
//   },
//   {
//     id: "colectia-bmw",
//     name: "Colectia BMW",
//     href: "/Colectia-Bmw",
//     image:
//       "https://images.unsplash.com/photo-1508685096489-7aacd43bd3b1?w=200&h=200&fit=crop",
//     description: "Classic BMW",
//   },
//   {
//     id: "colectia-bmw-i",
//     name: "Colectia BMW i",
//     href: "/Colectia-Bmw-I",
//     image:
//       "https://images.unsplash.com/photo-1546868871-7041f2a55e12?w=200&h=200&fit=crop",
//     description: "Innovative designs ",
//   },
//   {
//     id: "colectia-bmw-iconic",
//     name: "Colectia BMW Iconic",
//     href: "/Colectia-Bmw-Iconic",
//     image:
//       "https://images.unsplash.com/photo-1614164185128-e4ec99c436d7?w=200&h=200&fit=crop",
//     description: "Iconic collection",
//   },
//   {
//     id: "colectia-bmw-motorsport",
//     name: "Colectia BMW Motorsport",
//     href: "/Colectia-Bmw-Motorsport",
//     image:
//       "https://images.unsplash.com/photo-1508685096489-7aacd43bd3b1?w=200&h=200&fit=crop",
//     description: "Motorsport BMW branded ",
//   },
//   {
//     id: "colectia-Mini",
//     name: "Colectia Mini",
//     href: "/Colectia-Mini",
//     image:
//       "https://images.unsplash.com/photo-1546868871-7041f2a55e12?w=200&h=200&fit=crop",
//     description: "Innovative designs ",
//   },
//   {
//     id: "colectia-bmw-Special",
//     name: "Colectia BMW Special",
//     href: "/Colectia-Bmw-Special",
//     image:
//       "https://images.unsplash.com/photo-1614164185128-e4ec99c436d7?w=200&h=200&fit=crop",
//     description: "Special BMW  ",
//   },
//   {
//     id: "colectia-Mini-Theme",
//     name: "Colectia Mini Theme",
//     href: "/Colectia-Mini-Theme",
//     image:
//       "https://images.unsplash.com/photo-1508685096489-7aacd43bd3b1?w=200&h=200&fit=crop",
//     description: "Mini Theme  ",
//   },
//   {
//     id: "colectia-bmw-Athletics",
//     name: "Colectia BMW Athletics",
//     href: "/Colectia-Bmw-Athletics",
//     image:
//       "https://images.unsplash.com/photo-1546868871-7041f2a55e12?w=200&h=200&fit=crop",
//     description: "Athletics designs ",
//   },
//   {
//     id: "colectia-bmw-Yachtsport",
//     name: "Colectia BMW Yachtsport",
//     href: "/Colectia-Bmw-Yachtsport",
//     image:
//       "https://images.unsplash.com/photo-1614164185128-e4ec99c436d7?w=200&h=200&fit=crop",
//     description: "Exclusive Yachtsport ",
//   },
//   {
//     id: "colectia-bmw-Golfsport",
//     name: "Colectia BMW Golfsport",
//     href: "/Colectia-Bmw-Golfsport",
//     image:
//       "https://images.unsplash.com/photo-1508685096489-7aacd43bd3b1?w=200&h=200&fit=crop",
//     description: "BMW  Golfsport",
//   },
//   {
//     id: "colectia-bmw-Classic",
//     name: "Colectia BMW Classic",
//     href: "/Colectia-Bmw-Classic",
//     image:
//       "https://images.unsplash.com/photo-1546868871-7041f2a55e12?w=200&h=200&fit=crop",
//     description: "Classic designs",
//   },
//   {
//     id: "colectia-bmw-F",
//     name: "Colectia BMW F",
//     href: "/Colectia-Bmw-F",
//     image:
//       "https://images.unsplash.com/photo-1614164185128-e4ec99c436d7?w=200&h=200&fit=crop",
//     description: "Exclusive for enthusiasts",
//   },
//   {
//     id: "colectia-Motociclete-bmw",
//     name: "Colectia Motociclete",
//     href: "/Colectia-Motociclete-Bmw",
//     image:
//       "https://images.unsplash.com/photo-1508685096489-7aacd43bd3b1?w=200&h=200&fit=crop",
//     description: "Motociclete BMW",
//   },
// ];

// const accesoriiCategories = [
//   {
//     label: "Tuning",
//     subcategories: [
//       {
//         label: "Grup Motopropulsor",
//         href: "/Grup-Motopropulsor",
//       },
//       {
//         label: "sasiu",
//         href: "/sasiu",
//       },
//     ],
//   },
//   {
//     label: "Design Interior",
//     subcategories: [
//       {
//         label: "Ornamente Interior",
//         href: "/Ornamente-Interior",
//       },
//       {
//         label: "Pedale",
//         href: "/Pedale",
//       },
//       {
//         label: "Directie",
//         href: "/Directie",
//       },
//     ],
//   },
//   {
//     label: "Comunicare",
//     subcategories: [
//       {
//         label: "Telefon",
//         href: "/Telefon",
//       },
//       {
//         label: "Sisteme Navigatie",
//         href: "/Sisteme-Navigatie",
//       },
//       {
//         label: "Video",
//         href: "/Video",
//       },
//       {
//         label: "Date Navigatie",
//         href: "/Date-Navigatie",
//       },
//       {
//         label: "Audio",
//         href: "/Audio",
//       },
//       {
//         label: "Telematics",
//         href: "/Telematics",
//       },
//       {
//         label: "Instrumente",
//         href: "/Instrumente",
//       },
//       {
//         label: "Interfata",
//         href: "/Interfata",
//       },
//     ],
//   },
//   {
//     label: "Confort si Siguranta",
//     subcategories: [
//       {
//         label: "Protectie Vehicul",
//         href: "/Protectie-Vehicul",
//       },
//       {
//         label: "Lumini Interioare",
//         href: "/Lumini-Interioare",
//       },
//       {
//         label: "Sistem Avarii",
//         href: "/Sistem-Avarii",
//       },
//       {
//         label: "Scaune",
//         href: "/Scaune",
//       },
//       {
//         label: "Asistenta",
//         href: "/Asistenta",
//       },
//       {
//         label: "Protectia Ocupantilor",
//         href: "/Protectia-Ocupantilor",
//       },
//       {
//         label: "Climatizare",
//         href: "/Climatizare",
//       },
//     ],
//   },
//   {
//     label: "Documentatie",
//     subcategories: [
//       {
//         label: "Accesorii si Documente Montaj",
//         href: "/Accesorii-si-Documente-Montaj",
//       },
//       {
//         label: "Documentatie Masina",
//         href: "/Documentatie-Masina",
//       },
//       {
//         label: "Marketing",
//         href: "/Marketing",
//       },
//       {
//         label: "Cerinte Dealer",
//         href: "/Cerinte-Dealer",
//       },
//       {
//         label: "Documentatie Service",
//         href: "/Documentatie-Service",
//       },
//     ],
//   },
//   {
//     label: "Exterior Design",
//     subcategories: [
//       {
//         label: "Ornemanete Exterioare",
//         href: "/Ornemanete-Exterioare",
//       },
//       {
//         label: "Aerodinamica",
//         href: "/Aerodinamica",
//       },
//       {
//         label: "Lumini Exterioare",
//         href: "/Lumini-Exterioare",
//       },
//     ],
//   },
//   {
//     label: "Solutii De Transport",
//     subcategories: [
//       {
//         label: "Sisteme Transport",
//         href: "/Sisteme-Transport",
//       },
//       {
//         label: "Solutii Bagaje Habitaclu",
//         href: "/Solutii-Bagaje-Habitaclu",
//       },
//       {
//         label: "Accesorii Tractare",
//         href: "/Accesorii-Tractare",
//       },
//     ],
//   },
//   {
//     label: "Chimice",
//     subcategories: [
//       {
//         label: "Produse Vopsea",
//         href: "/Produse-Vopsea",
//       },
//       {
//         label: "Produce Service si Reparatii",
//         href: "/Produce-Service-si-Reparatii",
//       },
//       {
//         label: "Ingrijire si Curatare",
//         href: "/Ingrijire-si-Curatare",
//       },
//       {
//         label: "Uleiuri",
//         href: "/Uleiuri",
//       },
//     ],
//   },
// ];

// const wheelsAndTiresCategories: SubCategory[] = [
//   {
//     id: "roti-complete-vara",
//     name: "Roti Complete Vara",
//     href: "/Roti-Complete-Vara",
//     icon: <Cylinder className="w-8 h-8" />,
//     description: "Complete summer wheel sets for your BMW",
//     color: "text-red-500",
//   },
//   {
//     id: "jante",
//     name: "Jante",
//     href: "/Jante",
//     icon: <Disc className="w-8 h-8" />,
//     description: "Premium BMW alloy wheels",
//     color: "text-blue-500",
//   },
//   {
//     id: "accesorii-roti",
//     name: "Accesorii Roti",
//     href: "/Accesorii-Roti",
//     icon: <Wrench className="w-8 h-8" />,
//     description: "Wheel accessories and components",
//     color: "text-green-500",
//   },
//   {
//     id: "anvelope-vara",
//     name: "Anvelope Vara",
//     href: "/Anvelope-Vara",
//     icon: <Cog className="w-8 h-8" />,
//     description: "High-performance summer tires",
//     color: "text-yellow-500",
//   },
//   {
//     id: "roti-complete-iarna",
//     name: "Roti Complete Iarna",
//     href: "/Roti-Complete-Iarna",
//     icon: <Cylinder className="w-8 h-8" />,
//     description: "Complete winter wheel sets",
//     color: "text-amber-500",
//   },
//   {
//     id: "anvelope-iarna",
//     name: "Anvelope Iarna",
//     href: "/Anvelope-Iarna",
//     icon: <Cog className="w-8 h-8" />,
//     description: "Winter tires for optimal traction",
//     color: "text-purple-500",
//   },
//   {
//     id: "anvelope-all-season",
//     name: "Anvelope All Season",
//     href: "/Anvelope-All-Season",
//     icon: <Cog className="w-8 h-8" />,
//     description: "All-season tires for year-round performance",
//     color: "text-cyan-500",
//   },
// ];

// const revizieCategories: SubCategory[] = [
//   {
//     id: "brakes",
//     name: "Brake System",
//     href: "#brakes",
//     icon: <Disc className="w-8 h-8" />,
//     description: "High-performance brake discs and calipers",
//     color: "text-red-500",
//   },
//   {
//     id: "turbo",
//     name: "Turbocharger",
//     href: "#turbo",
//     icon: <Gauge className="w-8 h-8" />,
//     description: "Boost your engine's performance",
//     color: "text-blue-500",
//   },
//   {
//     id: "filters",
//     name: "Air Filters",
//     href: "#filters",
//     icon: <Filter className="w-8 h-8" />,
//     description: "High-flow air filtration systems",
//     color: "text-green-500",
//   },
//   {
//     id: "spark-plugs",
//     name: "Spark Plugs",
//     href: "#spark-plugs",
//     icon: <Zap className="w-8 h-8" />,
//     description: "Premium ignition components",
//     color: "text-yellow-500",
//   },
//   {
//     id: "oil-system",
//     name: "Oil System",
//     href: "#oil-system",
//     icon: <Droplet className="w-8 h-8" />,
//     description: "Complete oil management solutions",
//     color: "text-amber-500",
//   },
//   {
//     id: "battery",
//     name: "Battery",
//     href: "#battery",
//     icon: <Battery className="w-8 h-8" />,
//     description: "High-capacity power solutions",
//     color: "text-purple-500",
//   },
//   {
//     id: "cooling",
//     name: "Cooling System",
//     href: "#cooling",
//     icon: <Fan className="w-8 h-8" />,
//     description: "Advanced cooling components",
//     color: "text-cyan-500",
//   },
//   {
//     id: "tools",
//     name: "Special Tools",
//     href: "#tools",
//     icon: <Wrench className="w-8 h-8" />,
//     description: "Specialized BMW maintenance tools",
//     color: "text-gray-500",
//   },
//   {
//     id: "transmission",
//     name: "Transmission",
//     href: "#transmission",
//     icon: <Cog className="w-8 h-8" />,
//     description: "Complete transmission solutions",
//     color: "text-indigo-500",
//   },
//   {
//     id: "engine-block",
//     name: "Engine Block",
//     href: "#engine-block",
//     icon: <Cylinder className="w-8 h-8" />,
//     description: "Core engine components",
//     color: "text-orange-500",
//   },
// ];

// const caroserieCategories: SubCategory[] = [
//   { id: "oglinda-usa", name: "Oglinda Usa", href: "/Oglinda-Usa" },
//   { id: "piese-ghidaj", name: "Piese Ghidaj", href: "/Piese-Ghidaj" },
//   {
//     id: "diverse-piese-usi",
//     name: "Diverse Piese Usi",
//     href: "/Diverse-Piese-Usi",
//   },
//   { id: "radiator-ulei", name: "Radiator Ulei", href: "/Radiator-Ulei" },
//   { id: "chedere-usi", name: "Chedere Usi", href: "/Chedere-Usi" },
//   { id: "diverse-piese", name: "Diverse Piese", href: "/Diverse-Piese" },
//   { id: "caroserie", name: "Caroserie", href: "/Caroserie" },
//   { id: "grile", name: "Grile", href: "/Grile" },
//   { id: "parbriz", name: "Parbriz", href: "/Parbriz" },
//   { id: "embleme", name: "Embleme", href: "/Embleme" },
//   {
//     id: "ornamente-caroserie",
//     name: "Ornamente Caroserie",
//     href: "/Ornamente-Caroserie",
//   },
//   { id: "capac-fata", name: "Capac Fata", href: "/Capac-Fata" },
//   {
//     id: "ornamente-si-chedere",
//     name: "Ornamente si Chedere",
//     href: "/Ornamente-si-Chedere",
//   },
//   {
//     id: "absorbant-soc-fata",
//     name: "Absorbant soc Fata",
//     href: "/Absorbant-soc-Fata",
//   },
//   { id: "airbag", name: "Airbag", href: "/Airbag" },
//   { id: "radiator", name: "Radiator", href: "/Radiator" },
//   { id: "capac-spate", name: "Capac Spate", href: "/Capac-Spate" },
//   { id: "lumini-spate", name: "Lumini Spate", href: "/Lumini-Spate" },
//   {
//     id: "absorbant-soc-spate",
//     name: "Absorbant soc Spate",
//     href: "/Absorbant-soc-Spate",
//   },
//   {
//     id: "diverse-piese-radiator",
//     name: "Diverse Piese Radiator",
//     href: "/Diverse-Piese-Radiator",
//   },
//   { id: "accidente", name: "Accidente", href: "/Accidente" },
//   { id: "baie-ulei", name: "Baie Ulei", href: "/Baie-Ulei" },
//   { id: "curele", name: "Curele", href: "/Curele" },
//   { id: "faruri", name: "Faruri", href: "/Faruri" },
//   { id: "aripa-fata", name: "Aripa Fata", href: "/Aripa-Fata" },
//   { id: "semnalizatoare", name: "Semnalizatoare", href: "/Semnalizatoare" },
//   { id: "usa-spate", name: "Usa Spate", href: "/Usa-Spate" },
//   { id: "aripa-spate", name: "Aripa Spate", href: "/Aripa-Spate" },
//   {
//     id: "panouri-fata-spate",
//     name: "Panouri Fata Spate",
//     href: "/Panouri-Fata-Spate",
//   },
//   { id: "rezervor", name: "Rezervor", href: "/Rezervor" },
//   { id: "lumini-ceata", name: "Lumini Ceata", href: "/Lumini-Ceata" },
//   { id: "lumini-frana", name: "Lumini Frana", href: "/Lumini-Frana" },
//   {
//     id: "ventilator-si-cuplaj",
//     name: "Ventilator si Cuplaj",
//     href: "/Ventilator-si-Cuplaj",
//   },
//   { id: "usa-fata", name: "Usa Fata", href: "/Usa-Fata" },
//   {
//     id: "ornamente-cauciuc",
//     name: "Ornamente Cauciuc",
//     href: "/Ornamente-Cauciuc",
//   },
// ];

// const generaleCategories: SubCategory[] = [
//   { id: "panou-interior", name: "Panou Interior", href: "/Panou-Interior" },
//   { id: "fata-de-usa", name: "Fata De Usa", href: "/Fata-De-Usa" },
//   { id: "motor", name: "Motor", href: "/Motor" },
//   { id: "chiulasa", name: "Chiulasa", href: "/Chiulasa" },
//   { id: "izolatii", name: "Izolatii", href: "/Izolatii" },
//   {
//     id: "echipament-interior",
//     name: "Echipament Interior",
//     href: "/Echipament-Interior",
//   },
//   { id: "conducte", name: "Conducte", href: "/Conducte" },
//   { id: "galerie-admisie", name: "Galerie Admisie", href: "/Galerie-Admisie" },
//   { id: "suspensie-motor", name: "Suspensie Motor", href: "/Suspensie-Motor" },
//   {
//     id: "articole-diverse-reparare",
//     name: "Articole Diverse Reparare",
//     href: "/Articole-Diverse-Reparare",
//   },
//   { id: "trapa-hard-top", name: "Trapa Hard Top", href: "/Trapa-Hard-Top" },
//   { id: "climatizare", name: "Climatizare", href: "/Climatizare" },
//   { id: "arc", name: "Arc", href: "/Arc" },
//   {
//     id: "schimbator-viteze",
//     name: "Schimbator Viteze",
//     href: "/Schimbator-Viteze",
//   },
//   { id: "scaune-fata", name: "Scaune Fata", href: "/Scaune-Fata" },
//   { id: "injectie", name: "Injectie", href: "/Injectie" },
//   { id: "reparatii", name: "Reparatii", href: "/Reparatii" },
//   { id: "unitate-abs", name: "Unitate Abs", href: "/Unitate-Abs" },
//   { id: "garnituri", name: "Garnituri", href: "/Garnituri" },
//   { id: "incuietoare-usa", name: "Incuietoare Usa", href: "/Incuietoare-Usa" },
//   {
//     id: "echipament-motor-si-sasiu",
//     name: "Echipament Motor si sasiu",
//     href: "/Echipament-Motor-si-sasiu",
//   },
//   { id: "cutie-de-viteze", name: "Cutie De Viteze", href: "/Cutie-De-Viteze" },
//   {
//     id: "piese-diverse-motor",
//     name: "Piese Diverse Motor",
//     href: "/Piese-Diverse-Motor",
//   },
//   {
//     id: "set-reparatie-directie",
//     name: "Set Reparatie Directie",
//     href: "/Set-Reparatie-Directie",
//   },
//   {
//     id: "mecanism-angrenare",
//     name: "Mecanism Angrenare",
//     href: "/Mecanism-Angrenare",
//   },
//   { id: "cutie-automata", name: "Cutie Automata", href: "/Cutie-Automata" },
//   { id: "evacuare", name: "Evacuare", href: "/Evacuare" },
//   { id: "kit-reparatie", name: "Kit Reparatie", href: "/Kit-Reparatie" },
//   { id: "lubrifiere", name: "Lubrifiere", href: "/Lubrifiere" },
//   { id: "planetare", name: "Planetare", href: "/Planetare" },
//   {
//     id: "recirculare-gaze",
//     name: "Recirculare Gaze",
//     href: "/Recirculare-Gaze",
//   },
//   {
//     id: "controlul-supapelor",
//     name: "Controlul Supapelor",
//     href: "/Controlul-Supapelor",
//   },
//   { id: "scaune-spate", name: "Scaune Spate", href: "/Scaune-Spate" },
//   { id: "cutie-manuala", name: "Cutie Manuala", href: "/Cutie-Manuala" },
//   { id: "punte-fata", name: "Punte Fata", href: "/Punte-Fata" },
//   { id: "parasolar", name: "Parasolar", href: "/Parasolar" },
//   { id: "punte-spate", name: "Punte Spate", href: "/Punte-Spate" },
//   {
//     id: "pompa-servodirectie",
//     name: "Pompa Servodirectie",
//     href: "/Pompa-Servodirectie",
//   },
//   { id: "turbocompresor", name: "Turbocompresor", href: "/Turbocompresor" },
//   {
//     id: "diverse-piese-sistem-franare",
//     name: "Diverse Piese Sistem Franare",
//     href: "/Diverse-Piese-Sistem-Franare",
//   },
//   { id: "bascule", name: "Bascule", href: "/Bascule" },
//   {
//     id: "cutie-depozitare",
//     name: "Cutie Depozitare",
//     href: "/Cutie-Depozitare",
//   },
//   { id: "caseta-directie", name: "Caseta Directie", href: "/Caseta-Directie" },
//   { id: "curea", name: "Curea", href: "/Curea" },
//   {
//     id: "accesorii-roti-si-anvelope",
//     name: "Accesorii Roti si Anvelope",
//     href: "/Accesorii-Roti-si-Anvelope",
//   },
//   { id: "cotiera", name: "Cotiera", href: "/Cotiera" },
//   { id: "supape", name: "Supape", href: "/Supape" },
//   { id: "termostat", name: "Termostat", href: "/Termostat" },
//   { id: "piston", name: "Piston", href: "/Piston" },
//   { id: "stabilizator", name: "Stabilizator", href: "/Stabilizator" },
//   { id: "piese-standard", name: "Piese Standard", href: "/Piese-Standard" },
//   {
//     id: "montant-punte-spate",
//     name: "Montant Punte Spate",
//     href: "/Montant-Punte-Spate",
//   },
//   { id: "catalizator", name: "Catalizator", href: "/Catalizator" },
//   { id: "torpedou", name: "Torpedou", href: "/Torpedou" },
//   { id: "bielete", name: "Bielete", href: "/Bielete" },
//   {
//     id: "garnitura-chiulasa",
//     name: "Garnitura Chiulasa",
//     href: "/Garnitura-Chiulasa",
//   },
//   {
//     id: "galerie-evacuare",
//     name: "Galerie Evacuare",
//     href: "/Galerie-Evacuare",
//   },
//   {
//     id: "alte-module-control",
//     name: "Alte Module Control",
//     href: "/Alte-Module-Control",
//   },
//   { id: "carter", name: "Carter", href: "/Carter" },
//   {
//     id: "pregatire-amestec",
//     name: "Pregatire Amestec",
//     href: "/Pregatire-Amestec",
//   },
//   { id: "debitmetru", name: "Debitmetru", href: "/Debitmetru" },
//   {
//     id: "bujii-incandescente",
//     name: "Bujii Incandescente",
//     href: "/Bujii-Incandescente",
//   },
//   { id: "huse-scaun", name: "Huse Scaun", href: "/Huse-Scaun" },
//   { id: "manson-cauciuc", name: "Manson Cauciuc", href: "/Manson-Cauciuc" },
// ];

// const electroniceCategories: SubCategory[] = [
//   { id: "cablaje", name: "Cablaje", href: "/Cablaje" },
//   { id: "hifi", name: "Hifi", href: "/Hifi" },
//   { id: "comutatoare", name: "Comutatoare", href: "/Comutatoare" },
//   {
//     id: "diverse-piese-electrice",
//     name: "Diverse Piese Electrice",
//     href: "/Diverse-Piese-Electrice",
//   },
//   {
//     id: "cablaje-aprindere",
//     name: "Cablaje Aprindere",
//     href: "/Cablaje-Aprindere",
//   },
//   { id: "aprindere", name: "Aprindere", href: "/Aprindere" },
//   { id: "alternator", name: "Alternator", href: "/Alternator" },
//   { id: "electromotor", name: "Electromotor", href: "/Electromotor" },
//   {
//     id: "control-climatizare",
//     name: "Control Climatizare",
//     href: "/Control-Climatizare",
//   },
//   {
//     id: "comunicatii-mobile",
//     name: "Comunicatii Mobile",
//     href: "/Comunicatii-Mobile",
//   },
//   {
//     id: "modul-motor-electric",
//     name: "Modul Motor Electric",
//     href: "/Modul-Motor-Electric",
//   },
//   { id: "display-uri", name: "Display Uri", href: "/Display-Uri" },
//   { id: "senzori-abs", name: "Senzori Abs", href: "/Senzori-Abs" },
//   {
//     id: "control-suspensie",
//     name: "Control Suspensie",
//     href: "/Control-Suspensie",
//   },
//   { id: "soclu-bec", name: "Soclu Bec", href: "/Soclu-Bec" },
//   { id: "baterie", name: "Baterie", href: "/Baterie" },
//   {
//     id: "componente-electrice",
//     name: "Componente Electrice",
//     href: "/Componente-Electrice",
//   },
//   {
//     id: "compentente-electrice-stergatoare",
//     name: "Compentente stergatoare",
//     href: "/Compentente-Electrice-stergatoare",
//   },
//   {
//     id: "lumini-interioare",
//     name: "Lumini Interioare",
//     href: "/Lumini-Interioare",
//   },
//   { id: "cd-dvd-player", name: "Cd Dvd Player", href: "/Cd-Dvd-Player" },
//   {
//     id: "monitor-control",
//     name: "Monitor Control",
//     href: "/Monitor-Control",
//   },
//   { id: "sisteme-alarma", name: "Sisteme Alarma", href: "/Sisteme-Alarma" },
//     name: "Modul Motor Electric",
//     href: "/Modul-Motor-Electric",
//   },
//   { id: "display-uri", name: "Display Uri", href: "/Display-Uri" },
//   { id: "senzori-abs", name: "Senzori Abs", href: "/Senzori-Abs" },
//   {
//     id: "control-suspensie",
//     name: "Control Suspensie",
//     href: "/Control-Suspensie",
//   },
//   { id: "soclu-bec", name: "Soclu Bec", href: "/Soclu-Bec" },
//   { id: "baterie", name: "Baterie", href: "/Baterie" },
//   {
//     id: "componente-electrice",
//     name: "Componente Electrice",
//     href: "/Componente-Electrice",
//   },
//   {
//     id: "compentente-electrice-stergatoare",
//     name: "Compentente stergatoare",
//     href: "/Compentente-Electrice-stergatoare",
//   },
//   {
//     id: "lumini-interioare",
//     name: "Lumini Interioare",
//     href: "/Lumini-Interioare",
//   },
//   { id: "cd-dvd-player", name: "Cd Dvd Player", href: "/Cd-Dvd-Player" },
//   {
//     id: "monitor-control",
//     name: "Monitor Control",
//     href: "/Monitor-Control",
//   },
//     id: "modul-motor-electric",
//     name: "Modul Motor Electric",
//     href: "/Modul-Motor-Electric",
//   },
//   { id: "display-uri", name: "Display Uri", href: "/Display-Uri" },
//   { id: "senzori-abs", name: "Senzori Abs", href: "/Senzori-Abs" },
//   {
//     id: "control-suspensie",
//     name: "Control Suspensie",
//     href: "/Control-Suspensie",
//   },
//   { id: "soclu-bec", name: "Soclu Bec", href: "/Soclu-Bec" },
//   { id: "baterie", name: "Baterie", href: "/Baterie" },
//   {
//     id: "componente-electrice",
//     name: "Componente Electrice",
//     href: "/Componente-Electrice",
//   },
//   {
//     id: "compentente-electrice-stergatoare",
//     name: "Compentente Electrice stergatoare",
//     href: "/Compentente-Electrice-stergatoare",
//   },
//   {
//     id: "lumini-interioare",
//     name: "Lumini Interioare",
//     href: "/Lumini-Interioare",
//   },
//   { id: "cd-dvd-player", name: "Cd Dvd Player", href: "/Cd-Dvd-Player" },
//   { id: "monitor-control", name: "Monitor Control", href: "/Monitor-Control" },
//   { id: "sisteme-alarma", name: "Sisteme Alarma", href: "/Sisteme-Alarma" },
//   {
//     id: "calculator-far-xenon",
//     name: "Calculator Far Xenon",
//     href: "/Calculator-Far-Xenon",
//   },
//   { id: "instrumente", name: "Instrumente", href: "/Instrumente" },
//   {
//     id: "macarale-electrice",
//     name: "Macarale Electrice",
//     href: "/Macarale-Electrice",
//   },
//   { id: "radio", name: "Radio", href: "/Radio" },
//   { id: "unitate-airbag", name: "Unitate Airbag", href: "/Unitate-Airbag" },
//   {
//     id: "rezistori-senzori",
//     name: "Rezistori Senzori",
//     href: "/Rezistori-Senzori",
//   },
//   { id: "releu-bujii", name: "Releu Bujii", href: "/Releu-Bujii" },
//   { id: "navigatie", name: "Navigatie", href: "/Navigatie" },
//   {
//     id: "unitate-control-injectie",
//     name: "Unitate Control Injectie",
//     href: "/Unitate-Control-Injectie",
//   },
//   {
//     id: "control-cutie-automata",
//     name: "Control Cutie Automata",
//     href: "/Control-Cutie-Automata",
//   },
//   {
//     id: "computer-comunicare",
//     name: "Computer Comunicare",
//     href: "/Computer-Comunicare",
//   },
//   {
//     id: "pompa-combustibil",
//     name: "Pompa Combustibil",
//     href: "/Pompa-Combustibil",
//   },
//   {
//     id: "sisteme-electronice-blocare",
//     name: "Sisteme Electronice Blocare",
//     href: "/Sisteme-Electronice-Blocare",
//   },
//   { id: "senzori", name: "Senzori", href: "/Senzori" },
//   { id: "computer-bord", name: "Computer Bord", href: "/Computer-Bord" },
//   {
//     id: "bobine-aprindere",
//     name: "Bobine Aprindere",
//     href: "/Bobine-Aprindere",
//   },
//   { id: "software", name: "Software", href: "/Software" },
//   { id: "becuri", name: "Becuri", href: "/Becuri" },
// ];

// const consumabileCategories: SubCategory[] = [
//   {
//     id: "filtru-aer",
//     name: "Filtru Aer",
//     href: "/Filtru-Aer",
//     icon: <Wind className="w-8 h-8" />,
//     description: "Filtre de aer pentru motorul tău BMW",
//     color: "text-blue-500",
//   },
//   {
//     id: "filtru-ulei",
//     name: "Filtru Ulei",
//     href: "/Filtru-Ulei",
//     icon: <Droplets className="w-8 h-8" />,
//     description: "Filtre de ulei de înaltă calitate",
//     color: "text-yellow-500",
//   },
//   {
//     id: "filtru-combustibil",
//     name: "Filtru Combustibil",
//     href: "/Filtru-Combustibil",
//     icon: <Filter className="w-8 h-8" />,
//     description: "Filtre de combustibil pentru performanță optimă",
//     color: "text-red-500",
//   },
//   {
//     id: "kit-uri-service",
//     name: "Kit Uri Service",
//     href: "/Kit-Uri-Service",
//     icon: <Package className="w-8 h-8" />,
//     description: "Kituri complete pentru service",
//     color: "text-green-500",
//   },
//   {
//     id: "bujii",
//     name: "Bujii",
//     href: "/Bujii",
//     icon: <Sparkles className="w-8 h-8" />,
//     description: "Bujii pentru aprindere perfectă",
//     color: "text-orange-500",
//   },
//   {
//     id: "pompa-apa",
//     name: "Pompa Apa",
//     href: "/Pompa-Apa",
//     icon: <Droplet className="w-8 h-8" />,
//     description: "Pompe de apă pentru sistemul de răcire",
//     color: "text-cyan-500",
//   },
//   {
//     id: "ambreiaj",
//     name: "Ambreiaj",
//     href: "/Ambreiaj",
//     icon: <Disc className="w-8 h-8" />,
//     description: "Sisteme de ambreiaj de înaltă performanță",
//     color: "text-purple-500",
//   },
//   {
//     id: "piese-de-uzura-diverse",
//     name: "Piese De Uzura Diverse",
//     href: "/Piese-De-Uzura-Diverse",
//     icon: <Wrench className="w-8 h-8" />,
//     description: "Diverse piese de uzură pentru întreținere",
//     color: "text-gray-500",
//   },
//   {
//     id: "amortizor",
//     name: "Amortizor",
//     href: "/Amortizor",
//     icon: <Waves className="w-8 h-8" />,
//     description: "Amortizoare pentru confort și performanță",
//     color: "text-indigo-500",
//   },
//   {
//     id: "senzori-uzura",
//     name: "Senzori Uzura",
//     href: "/Senzori-Uzura",
//     icon: <Gauge className="w-8 h-8" />,
//     description: "Senzori pentru monitorizarea uzurii",
//     color: "text-blue-600",
//   },
//   {
//     id: "placute-frana",
//     name: "Placute Frana",
//     href: "/Placute-Frana",
//     icon: <Hammer className="w-8 h-8" />,
//     description: "Plăcuțe de frână pentru oprire sigură",
//     color: "text-red-600",
//   },
//   {
//     id: "discuri-frana",
//     name: "Discuri Frana",
//     href: "/Discuri-Frana",
//     icon: <Disc className="w-8 h-8" />,
//     description: "Discuri de frână de înaltă performanță",
//     color: "text-gray-600",
//   },
//   {
//     id: "stergatoare",
//     name: "Stergatoare",
//     href: "/stergatoare",
//     icon: <Waves className="w-8 h-8" />,
//     description: "Ștergătoare pentru vizibilitate perfectă",
//     color: "text-blue-400",
//   },
//   {
//     id: "curea-ventilator",
//     name: "Curea Ventilator",
//     href: "/Curea-Ventilator",
//     icon: <Fan className="w-8 h-8" />,
//     description: "Curele pentru ventilator",
//     color: "text-green-600",
//   },
//   {
//     id: "ventilator-si-curea",
//     name: "Ventilator si Curea",
//     href: "/Ventilator-si-Curea",
//     icon: <Fan className="w-8 h-8" />,
//     description: "Seturi complete de ventilator și curea",
//     color: "text-teal-500",
//   },
//   {
//     id: "supape-garnituri",
//     name: "Supape Garnituri",
//     href: "/Supape-Garnituri",
//     icon: <Cylinder className="w-8 h-8" />,
//     description: "Supape și garnituri pentru motor",
//     color: "text-amber-500",
//   },
//   {
//     id: "bucsi",
//     name: "Bucsi",
//     href: "/Bucsi",
//     icon: <Cylinder className="w-8 h-8" />,
//     description: "Bucși pentru suspensie și direcție",
//     color: "text-yellow-600",
//   },
//   {
//     id: "rulment-si-butuc",
//     name: "Rulment si Butuc",
//     href: "/Rulment-si-Butuc",
//     icon: <Disc className="w-8 h-8" />,
//     description: "Rulmenți și butuci pentru roți",
//     color: "text-orange-600",
//   },
//   {
//     id: "amortizoare",
//     name: "Amortizoare",
//     href: "/Amortizoare",
//     icon: <Waves className="w-8 h-8" />,
//     description: "Amortizoare pentru toate modelele BMW",
//     color: "text-violet-500",
//   },
//   {
//     id: "roti-si-anvelope",
//     name: "Roti si Anvelope",
//     href: "/Roti-si-Anvelope",
//     icon: <Car className="w-8 h-8" />,
//     description: "Roți și anvelope complete",
//     color: "text-slate-600",
//   },
// ];

// const MegaMenu: React.FC<MegaMenuProps> = ({
//   isOpen = true,
//   onClose = () => {},
//   defaultTab,
// }) => {
//   if (!isOpen) return null;

//   const renderLifestyleContent = () => (
//     <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
//       {lifestyleCategories.map((item) => (
//         <a
//           key={item.id}
//           href={item.href}
//           className="group p-4 hover:bg-gray-50 rounded-lg transition-colors"
//            //className="group flex flex-col items-center py-2" FOOTLOCKER
//         >
//           <div 
//             className="h-32 mb-3 overflow-hidden rounded-lg"
//             //className="h-24 w-24 mb-1 overflow-hidden rounded-lg bg-gray-100" FOOTLOCKER
//             >
//             <img
//               src={item.image}
//               alt={item.name}
//               className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
//             />
//           </div>
//           <h3 
//           className="font-semibold text-gray-900 mb-1"
//           //className="p-2 text-xs bg-[#f1f5f9] hover:bg-gray-200 rounded-md transition-colors border-b-2 border-transparent font-medium flex items-center gap-1" FOOTLOCKER
//           >{item.name}</h3>
//           <p className="text-sm text-gray-600">{item.description}</p>
//         </a>
//       ))}
//     </div>
//   );

//   const renderAccesoriiContent = () => (
//     <div className="w-full">
//       <Tabs defaultValue={accesoriiCategories[0].label}>
//         <TabsList className="w-full border-b border-gray-200 mb-4 flex flex-wrap">
//           {accesoriiCategories.map((category) => (
//             <TabsTrigger
//               key={category.label}
//               value={category.label}
//               className="px-4"
//             >
//               {category.label.replace(" Accesorii", "")}
//             </TabsTrigger>
//           ))}
//         </TabsList>

//         {accesoriiCategories.map((category) => (
//           <TabsContent
//             key={category.label}
//             value={category.label}
//             className="grid grid-cols-3 gap-6"
//           >
//             {category.subcategories.map((subcategory) => (
//               <a
//                 key={subcategory.href}
//                 href={subcategory.href}
//                 className="group p-4 hover:bg-gray-50 rounded-lg transition-colors border border-gray-100 hover:border-gray-200 hover:shadow-md"
//               >
//                 <div className="h-24 mb-3 overflow-hidden rounded-lg bg-gray-100 flex items-center justify-center">
//                   <img
//                     src={`https://images.unsplash.com/photo-1546868871-7041f2a55e12?w=200&h=150&fit=crop&q=80`}
//                     alt={subcategory.label}
//                     className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
//                   />
//                 </div>
//                 <h3 className="font-semibold text-gray-900 mb-1">
//                   {subcategory.label}
//                 </h3>
//                 <p className="text-sm text-gray-600">
//                   Piese și accesorii BMW originale pentru{" "}
//                   {subcategory.label.toLowerCase()}
//                 </p>
//               </a>
//             ))}
//           </TabsContent>
//         ))}
//       </Tabs>
//     </div>
//   );

//   const renderWheelsAndTiresContent = () => (
//     <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
//       {wheelsAndTiresCategories.map((item) => (
//         <a
//           key={item.id}
//           href={item.href}
//           className="group p-4 hover:bg-gray-50 rounded-lg transition-all duration-300 border border-gray-100 hover:border-gray-200 hover:shadow-md"
//         >
//           <div
//             className={`${item.color} mb-3 transform group-hover:scale-110 transition-transform duration-300`}
//           >
//             {item.icon}
//           </div>
//           <h3 className="font-semibold text-gray-900 mb-1">{item.name}</h3>
//           <p className="text-sm text-gray-600">{item.description}</p>
//         </a>
//       ))}
//     </div>
//   );

//   const renderConsumabileContent = () => (
//     <div className="grid grid-cols-5 gap-8">
//       {consumabileCategories.map((item) => (
//         <a
//           key={item.id}
//           href={item.href}
//           className="p-2 bg-gray-50 hover:bg-gray-100 rounded-md transition-colors border-b-2 border-transparent hover:border-[#0066B1] font-medium flex items-center gap-2"
//         >
//           <div className={`${item.color} flex-shrink-0`}>
//             {item.icon ? (
//               React.cloneElement(item.icon, { className: "w-4 h-8" })
//             ) : null}
//           </div>
//           <span className="truncate">{item.name}</span>
//         </a>
//       ))}
//     </div>
//   );

//   const renderUniversaleContent = () => (
//     <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
//       {revizieCategories.map((item) => (
//         <a
//           key={item.id}
//           href={item.href}
//           className="group p-4 hover:bg-gray-50 rounded-lg transition-all duration-300 border border-gray-100 hover:border-gray-200 hover:shadow-md"
//         >
//           <div
//             className={`${item.color} mb-3 transform group-hover:scale-110 transition-transform duration-300`}
//           >
//             {item.icon}
//           </div>
//           <h3 className="font-semibold text-gray-900 mb-1">{item.name}</h3>
//           <p className="text-sm text-gray-600">{item.description}</p>
//         </a>
//       ))}
//     </div>
//   );

//   const renderCaroserieContent = () => (
//     <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 divide-x divide-gray-200">
//       {caroserieCategories.map((item) => (
//         <a
//           key={item.id}
//           href={item.href}
//           className="p-2 hover:bg-gray-50 rounded-md transition-colors border-b-2 border-[#E6E6E6] hover:border-[#0066B1] font-medium relative group"
//         >
//           {item.name}
//           <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#0066B1] group-hover:w-full transition-all duration-300"></span>
//         </a>
//       ))}
//     </div>
//   );

//   const renderGeneraleContent = () => (
//     <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 [&>*:nth-child(5n+1)]:border-l-0 [&>*]:border-l [&>*]:border-gray-200 [&>*]:pl-4">
//       {generaleCategories.map((item) => (
//         <a
//           key={item.id}
//           href={item.href}
//           className="p-2 bg-gray-50 hover:bg-gray-100 rounded-md transition-colors border-b border-gray-300 hover:text-[#0066B1] font-medium"
//         >
//           {item.name}
//         </a>
//       ))}
//     </div>
//   );

//   const renderElectroniceContent = () => (
//     <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
//       {electroniceCategories.map((item) => (
//         <a
//           key={item.id}
//           href={item.href}
//           className="p-2 hover:bg-gray-50 rounded-md transition-colors border-b-2 border-transparent hover:border-[#0066B1] font-medium"
//         >
//           {item.name}
//         </a>
//       ))}
//     </div>
//   );

//   return (
//     <motion.div
//       initial={{ opacity: 0, y: -10 }}
//       animate={{ opacity: 1, y: 0 }}
//       exit={{ opacity: 0, y: -10 }}
//       transition={{ duration: 0.3 }}
//       className="absolute top-full left-0 w-full bg-white shadow-lg z-50"
//     >
//       <div className="max-w-7xl mx-auto p-4 md:p-6">
//         <ScrollArea className="h-[400px] pr-4">
//           {defaultTab === "lifestyle" && renderLifestyleContent()}
//           {defaultTab === "accesorii" && renderAccesoriiContent()}
//           {defaultTab === "wheels-tires" && renderWheelsAndTiresContent()}
//           {defaultTab === "universale" && renderUniversaleContent()}
//           {defaultTab === "caroserie" && renderCaroserieContent()}
//           {defaultTab === "generale" && renderGeneraleContent()}
//           {defaultTab === "electronice" && renderElectroniceContent()}
//           {defaultTab === "consumabile" && renderConsumabileContent()}
//         </ScrollArea>
//       </div>
//     </motion.div>
//   );
// };

// export default MegaMenu;



// import React from "react";
// import { motion } from "framer-motion";
// import { ScrollArea } from "@/components/ui/scroll-area";
// import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
// import {
//   Disc,
//   Gauge,
//   Filter,
//   Zap,
//   Droplet,
//   Battery,
//   Fan,
//   Wrench,
//   Cog,
//   Cylinder,
// } from "lucide-react";

// interface SubCategory {
//   id: string;
//   name: string;
//   href: string;
//   icon?: React.ReactNode;
//   description: string;
//   color?: string;
//   image?: string;
// }

// interface MegaMenuProps {
//   isOpen?: boolean;
//   onClose?: () => void;
//   defaultTab?: string;
// }

// const lifestyleCategories = {
//   watches: [
//     {
//       id: "men",
//       name: "Men",
//       href: "/categorie",
//       image:
//         "https://images.unsplash.com/photo-1614164185128-e4ec99c436d7?w=200&h=200&fit=crop",
//       description: "Sophisticated timepieces for the modern gentleman",
//     },
//     {
//       id: "women",
//       name: "Women",
//       href: "#watches-women",
//       image:
//         "https://images.unsplash.com/photo-1508685096489-7aacd43bd3b1?w=200&h=200&fit=crop",
//       description: "Elegant watches designed for style and comfort",
//     },
//     {
//       id: "unisex",
//       name: "Unisex",
//       href: "#watches-unisex",
//       image:
//         "https://images.unsplash.com/photo-1546868871-7041f2a55e12?w=200&h=200&fit=crop",
//       description: "Versatile timepieces for everyone",
//     },
//   ],
//   tshirts: [
//     {
//       id: "classic",
//       name: "Classic",
//       href: "#t-shirts-classic",
//       image:
//         "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=200&h=200&fit=crop",
//       description: "Timeless BMW designs for everyday wear",
//     },
//     {
//       id: "sport",
//       name: "Sport",
//       href: "#t-shirts-sport",
//       image:
//         "https://images.unsplash.com/photo-1576566588028-4147f3842f27?w=200&h=200&fit=crop",
//       description: "Performance wear for active enthusiasts",
//     },
//     {
//       id: "premium",
//       name: "Premium",
//       href: "#t-shirts-premium",
//       image:
//         "https://images.unsplash.com/photo-1618354691373-d851c5c3a990?w=200&h=200&fit=crop",
//       description: "Luxury cotton blend with exclusive designs",
//     },
//   ],
// };

// const engineCategories: SubCategory[] = [
//   {
//     id: "brakes",
//     name: "Brake System",
//     href: "/categorie",
//     icon: <Disc className="w-8 h-8" />,
//     description: "High-performance brake discs and calipers",
//     color: "text-red-500",
//   },
//   {
//     id: "turbo",
//     name: "Turbocharger",
//     href: "#turbo",
//     icon: <Gauge className="w-8 h-8" />,
//     description: "Boost your engine's performance",
//     color: "text-blue-500",
//   },
//   {
//     id: "filters",
//     name: "Air Filters",
//     href: "#filters",
//     icon: <Filter className="w-8 h-8" />,
//     description: "High-flow air filtration systems",
//     color: "text-green-500",
//   },
//   {
//     id: "spark-plugs",
//     name: "Spark Plugs",
//     href: "#spark-plugs",
//     icon: <Zap className="w-8 h-8" />,
//     description: "Premium ignition components",
//     color: "text-yellow-500",
//   },
//   {
//     id: "oil-system",
//     name: "Oil System",
//     href: "#oil-system",
//     icon: <Droplet className="w-8 h-8" />,
//     description: "Complete oil management solutions",
//     color: "text-amber-500",
//   },
//   {
//     id: "battery",
//     name: "Battery",
//     href: "#battery",
//     icon: <Battery className="w-8 h-8" />,
//     description: "High-capacity power solutions",
//     color: "text-purple-500",
//   },
//   {
//     id: "cooling",
//     name: "Cooling System",
//     href: "#cooling",
//     icon: <Fan className="w-8 h-8" />,
//     description: "Advanced cooling components",
//     color: "text-cyan-500",
//   },
//   {
//     id: "tools",
//     name: "Special Tools",
//     href: "#tools",
//     icon: <Wrench className="w-8 h-8" />,
//     description: "Specialized BMW maintenance tools",
//     color: "text-gray-500",
//   },
//   {
//     id: "transmission",
//     name: "Transmission",
//     href: "#transmission",
//     icon: <Cog className="w-8 h-8" />,
//     description: "Complete transmission solutions",
//     color: "text-indigo-500",
//   },
//   {
//     id: "engine-block",
//     name: "Engine Block",
//     href: "#engine-block",
//     icon: <Cylinder className="w-8 h-8" />,
//     description: "Core engine components",
//     color: "text-orange-500",
//   },
// ];

// const maintenanceCategories = Array.from({ length: 40 }, (_, i) => ({
//   id: `maintenance-${i + 1}`,
//   name: `Value ${i + 1}`,
//   href: `#maintenance-${i + 1}`,
// }));

// const MegaMenu: React.FC<MegaMenuProps> = ({
//   isOpen = true,
//   onClose = () => {},
//   defaultTab,
// }) => {
//   if (!isOpen) return null;

//   const renderLifestyleContent = () => (
//     <div className="w-full">
//       <Tabs defaultValue="watches">
//         <TabsList className="w-full border-b border-gray-200 mb-4">
//           <TabsTrigger value="watches" className="px-4 py-2">
//             Watches
//           </TabsTrigger>
//           <TabsTrigger value="tshirts" className="px-4 py-2">
//             T-Shirts
//           </TabsTrigger>
//         </TabsList>
//         <TabsContent value="watches" className="grid grid-cols-3 gap-6">
//           {lifestyleCategories.watches.map((item) => (
//             <a
//               key={item.id}
//               href={item.href}
//               className="group p-4 hover:bg-gray-50 rounded-lg transition-colors"
//             >
//               <div className="h-32 mb-3 overflow-hidden rounded-lg">
//                 <img
//                   src={item.image}
//                   alt={item.name}
//                   className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
//                 />
//               </div>
//               <h3 className="font-semibold text-gray-900 mb-1">{item.name}</h3>
//               <p className="text-sm text-gray-600">{item.description}</p>
//             </a>
//           ))}
//         </TabsContent>
//         <TabsContent value="tshirts" className="grid grid-cols-3 gap-6">
//           {lifestyleCategories.tshirts.map((item) => (
//             <a
//               key={item.id}
//               href={item.href}
//               className="group p-4 hover:bg-gray-50 rounded-lg transition-colors"
//             >
//               <div className="h-32 mb-3 overflow-hidden rounded-lg">
//                 <img
//                   src={item.image}
//                   alt={item.name}
//                   className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
//                 />
//               </div>
//               <h3 className="font-semibold text-gray-900 mb-1">{item.name}</h3>
//               <p className="text-sm text-gray-600">{item.description}</p>
//             </a>
//           ))}
//         </TabsContent>
//       </Tabs>
//     </div>
//   );

//   const renderEngineContent = () => (
//     <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
//       {engineCategories.map((item) => (
//         <a
//           key={item.id}
//           href={item.href}
//           className="group p-4 hover:bg-gray-50 rounded-lg transition-all duration-300 border border-gray-100 hover:border-gray-200 hover:shadow-md"
//         >
//           <div
//             className={`${item.color} mb-3 transform group-hover:scale-110 transition-transform duration-300`}
//           >
//             {item.icon}
//           </div>
//           <h3 className="font-semibold text-gray-900 mb-1">{item.name}</h3>
//           <p className="text-sm text-gray-600">{item.description}</p>
//         </a>
//       ))}
//     </div>
//   );

//   const renderMaintenanceContent = () => (
//     <div className="grid grid-cols-4 gap-4">
//       {maintenanceCategories.map((item) => (
//         <a
//           key={item.id}
//           href={item.href}
//           className="p-2 hover:bg-gray-50 rounded-md transition-colors border-b-2 border-transparent hover:border-[#0066B1] font-medium"
//         >
//           {item.name}
//         </a>
//       ))}
//     </div>
//   );

//   return (
//     <motion.div
//       initial={{ opacity: 0, y: -10 }}
//       animate={{ opacity: 1, y: 0 }}
//       exit={{ opacity: 0, y: -10 }}
//       transition={{ duration: 0.3 }}
//       className="absolute top-full left-0 w-full bg-white shadow-lg z-50"
//     >
//       <div className="max-w-7xl mx-auto p-4 md:p-6">
//         <ScrollArea className="h-[400px] pr-4">
//           {defaultTab === "lifestyle" && renderLifestyleContent()}
//           {defaultTab === "engine" && renderEngineContent()}
//           {defaultTab === "maintenance" && renderMaintenanceContent()}
//         </ScrollArea>
//       </div>
//     </motion.div>
//   );
// };

// export default MegaMenu;
