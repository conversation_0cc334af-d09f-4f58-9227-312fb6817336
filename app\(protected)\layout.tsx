import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { ClerkProvider } from "@clerk/nextjs";
import { getCurrentDbUser } from "@/lib/auth";
import { getWishlistProductCodes } from "../getData/wishlist";
import { Providers } from "../providers";
import { WishlistProvider } from "../context/WishlistContext";
import { MainNavbar } from "../components/navbar/MainNavbar";
import MegaMenuAndCategory from "../components/navbar/MegaMenuCategory";
import Footer from "../components/footer/Footer";
import { Toaster } from "@/components/ui/sonner";


const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Automobile Bavaria : Piese auto BMW originale",
  description: "Piese auto BMW originale",
};


export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  // Use safe version that doesn't redirect to avoid loops
  const userId = await getCurrentDbUser();

  let wishlistProductCodes = new Set<string>();

  if (userId) {
      wishlistProductCodes = await getWishlistProductCodes(userId.id);
  }

  // Convert Set for the context (already a Set)
  const initialWishlistItems = wishlistProductCodes;

  return (
    <ClerkProvider>
      <html lang="en" suppressHydrationWarning>
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        >
          <Providers>
            <WishlistProvider initialItems={initialWishlistItems}>
              <div className="relative w-full shadow-md">
                <div className="max-w-[1640px] mx-auto">
                  <MainNavbar searchParams={{ query: "" }} />
                  <MegaMenuAndCategory />
                </div>
              </div>
              <div className="relative w-full">
                {children}
                <Footer />
              </div>
              <Toaster />
            </WishlistProvider>
          </Providers>
        </body>
      </html>
    </ClerkProvider>
  );
}


