import { NextResponse } from 'next/server';
import { auth, currentUser } from '@clerk/nextjs/server';
import { prisma, withRetry } from '@/lib/db';
import { UserApiResponse } from '@/types';
import { getClientIp } from '@/lib/utils';
import { getCurrentDbUser } from '@/lib/auth';
import { logger } from '@/lib/logger';

//i dont use it....for client components

export async function GET(req: Request) {

  const clientIp = getClientIp(req);
  const allowedIps = (process.env.ALLOWED_IPS_FOR_HEALTH_CHECK_ENDPOINT || '').split(',').map(ip => ip.trim());

  if (!clientIp || !allowedIps.includes(clientIp)) {
    logger.warn(`[api/user/me] IP not allowed: ${clientIp}`)
    return NextResponse.json({ status: 'forbidden', reason: 'IP not allowed' }, { status: 403 });
  }

  const user = await getCurrentDbUser();
  
  if (!user) {
    logger.warn(`[api/user/me] No user authenticated for ${clientIp}]`)
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    // Get the Clerk user
    const clerkUser = await currentUser();
    
    if (!clerkUser) {
      logger.warn(`[api/user/me] User not found for ${clientIp}]`)
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    // Find or create the user in our database with retry logic
    let dbUser = await withRetry(() => 
      prisma.user.findUnique({
        where: { externalId: clerkUser.id },
      })
    );
    
    if (!dbUser) {
      logger.info(`[api/user/me] User not found in database for ${clientIp}]`)
      dbUser = await withRetry(() => 
        prisma.user.create({
          data: {
            externalId: clerkUser.id,
            email: clerkUser.emailAddresses[0]?.emailAddress || '',
            firstName: clerkUser.firstName || '',
            lastName: clerkUser.lastName || '',
            profileImage: clerkUser.imageUrl || '',
            externalProvider: 'clerk',
            lastLoginAt: new Date(),
            loginCount: 1,
            lastActivityAt: new Date(),
          },
        })
      );
    } else {
      // Update login stats
      await withRetry(() => 
        prisma.user.update({
          where: { externalId: clerkUser.id },
          data: {
            lastActivityAt: new Date(),
          }
        })
      );
    }
    
    // Return both Clerk user and our database user
    const response: UserApiResponse = {
      clerkUser: {
        id: clerkUser.id,
        firstName: clerkUser.firstName,
        lastName: clerkUser.lastName,
        emailAddress: clerkUser.emailAddresses[0]?.emailAddress,
        imageUrl: clerkUser.imageUrl,
      },
      dbUser: dbUser as any
    };
    
    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    logger.error(`Error fetching current user for ${clientIp}: ${error}`);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

