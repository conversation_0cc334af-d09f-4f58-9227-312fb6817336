"use client";

import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Loader2, CheckCircle, AlertCircle, Bell, Mail, Smartphone, MessageSquare } from "lucide-react";
import { notificationPreferencesSchema, type NotificationPreferencesInput } from "@/lib/zod";
import { updateNotificationPreferences, type ActionResult } from "@/app/actions/account";
import { AccountSettingsData } from "@/app/getData/account-settings";

interface PreferencesFormProps {
  accountData: AccountSettingsData;
}

export default function PreferencesForm({ accountData }: PreferencesFormProps) {
  const [isPending, startTransition] = useTransition();
  const [result, setResult] = useState<ActionResult | null>(null);

  const {
    handleSubmit,
    watch,
    setValue,
    formState: { isDirty }
  } = useForm<NotificationPreferencesInput>({
    resolver: zodResolver(notificationPreferencesSchema),
    defaultValues: {
      emailNotifications: accountData.emailNotifications,
      pushNotifications: accountData.pushNotifications,
      smsNotifications: accountData.smsNotifications,
      newsletterOptIn: accountData.newsletterOptIn,
    }
  });

  const watchedValues = watch();

  const onSubmit = (data: NotificationPreferencesInput) => {
    startTransition(async () => {
      try {
        const result = await updateNotificationPreferences(data);
        setResult(result);
        
        if (result.success) {
          setTimeout(() => setResult(null), 3000);
        }
      } catch (error) {
        setResult({
          success: false,
          error: "A apărut o eroare neașteptată. Vă rugăm să încercați din nou."
        });
      }
    });
  };

  const handleSwitchChange = (field: keyof NotificationPreferencesInput, value: boolean) => {
    setValue(field, value, { shouldDirty: true });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Preferințe Notificări
        </CardTitle>
        <CardDescription>
          Gestionați modul în care doriți să primiți notificări și comunicări
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Success/Error Messages */}
          {result && (
            <Alert variant={result.success ? "default" : "destructive"}>
              {result.success ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <AlertDescription>
                {result.success 
                  ? "Preferințele au fost actualizate cu succes!" 
                  : result.error
                }
              </AlertDescription>
            </Alert>
          )}

          {/* Email Notifications */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-muted-foreground" />
                <div className="space-y-1">
                  <Label htmlFor="emailNotifications" className="text-base font-medium">
                    Notificări prin email
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Primiți notificări despre comenzi, actualizări de cont și alte informații importante
                  </p>
                </div>
              </div>
              <Switch
                id="emailNotifications"
                checked={watchedValues.emailNotifications}
                onCheckedChange={(checked) => handleSwitchChange("emailNotifications", checked)}
              />
            </div>

            <Separator />

            {/* Push Notifications */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Smartphone className="h-5 w-5 text-muted-foreground" />
                <div className="space-y-1">
                  <Label htmlFor="pushNotifications" className="text-base font-medium">
                    Notificări push
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Primiți notificări instant pe dispozitiv despre activitatea contului
                  </p>
                </div>
              </div>
              <Switch
                id="pushNotifications"
                checked={watchedValues.pushNotifications}
                onCheckedChange={(checked) => handleSwitchChange("pushNotifications", checked)}
              />
            </div>

            <Separator />

            {/* SMS Notifications */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <MessageSquare className="h-5 w-5 text-muted-foreground" />
                <div className="space-y-1">
                  <Label htmlFor="smsNotifications" className="text-base font-medium">
                    Notificări SMS
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Primiți SMS-uri pentru confirmări de comenzi și actualizări critice
                  </p>
                </div>
              </div>
              <Switch
                id="smsNotifications"
                checked={watchedValues.smsNotifications}
                onCheckedChange={(checked) => handleSwitchChange("smsNotifications", checked)}
                disabled={!accountData.phoneNumber}
              />
            </div>

            {!accountData.phoneNumber && (
              <p className="text-xs text-muted-foreground ml-8">
                Pentru a activa notificările SMS, adăugați un număr de telefon în secțiunea Profil
              </p>
            )}

            <Separator />

            {/* Newsletter */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-muted-foreground" />
                <div className="space-y-1">
                  <Label htmlFor="newsletterOptIn" className="text-base font-medium">
                    Newsletter și oferte
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Primiți newsletter-ul nostru cu oferte speciale, noutăți și sfaturi utile
                  </p>
                </div>
              </div>
              <Switch
                id="newsletterOptIn"
                checked={watchedValues.newsletterOptIn}
                onCheckedChange={(checked) => handleSwitchChange("newsletterOptIn", checked)}
              />
            </div>
          </div>

          {/* Information Alert */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Notă importantă:</strong> Anumite notificări critice (cum ar fi confirmările de comenzi și 
              actualizările de securitate) vor fi trimise indiferent de aceste setări pentru a vă proteja contul.
            </AlertDescription>
          </Alert>

          {/* Privacy Notice */}
          <div className="bg-muted/50 p-4 rounded-lg">
            <h4 className="text-sm font-medium mb-2">Confidențialitate și date</h4>
            <p className="text-xs text-muted-foreground">
              Respectăm confidențialitatea dvs. și nu vom împărtăși niciodată informațiile dvs. cu terți 
              fără consimțământul dvs. explicit. Puteți modifica aceste preferințe oricând. 
              Pentru mai multe informații, consultați{" "}
              <a href="/privacy" className="text-primary hover:underline">
                Politica de Confidențialitate
              </a>.
            </p>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button 
              type="submit" 
              disabled={isPending || !isDirty}
              className="bg-[#0066B1] hover:bg-[#004d85]"
            >
              {isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Se salvează...
                </>
              ) : (
                "Salvează preferințele"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
