import { getCurrentDbUser } from "@/lib/auth";

export default async function ProfilePage() {
  // This will handle authentication, active status, and suspension checks
  // It will redirect appropriately if any check fails
  const dbUser = await getCurrentDbUser();
  
  return (
    <div className="container mx-auto py-8">
      <div className=" p-6 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold mb-4">Your Profile</h1>
        
        <div className="space-y-4">
          <div>
            <h2 className="text-lg font-semibold">Account Information</h2>
            <p><strong>Name:</strong> {dbUser?.firstName} {dbUser?.lastName}</p>
            <p><strong>Email:</strong> {dbUser?.email}</p>
            <p><strong>Role:</strong> {dbUser?.role}</p>
          </div>
          
        </div>
      </div>
    </div>
  );
}
