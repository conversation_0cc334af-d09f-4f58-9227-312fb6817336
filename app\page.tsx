
import { getCurrentDbUser } from "@/lib/auth";
import ProductGrid from "./components/product/ProductGrid";
import { getFeaturedProducts } from "./getData/products";
import CategoryGrid from "./components/categories/CategoryGridLandingPage";
import HeroSection from "./components/banner/HeroSection";
import { getCategorySectionLandingPageBanners, getHeroBanners } from "./getData/banners";
import { redirect } from "next/navigation";
import { ProductCardInterface } from "@/types/product";

import { getPretSiStocBatch, getPricesFor4thBatch } from "@/lib/mssql/query";
import { getRecentOrders } from "./getData/user";

export default async function Home() {

   const user = await getCurrentDbUser()
  
    if (!user) {
      return redirect("/sign-in");
    }

    // 1 Determine if user gets special 4️⃣-level pricing
    const has4th =
      user.role.includes("fourLvlAdminAB") ||
      user.role.includes("fourLvlInregistratAB");

    // 2 Fetch user, main product, featured in parallel
    const [featured, ordered, heroBanners, categories] = await Promise.all([
      getFeaturedProducts(),
      user ? getRecentOrders(user.id) : Promise.resolve([]),
      getHeroBanners(),
      getCategorySectionLandingPageBanners()
    ]);
  
    // 3 Kick off all remaining I/O in parallel:
    const featuredSkus = featured.map((p) => p.Material_Number);
    const orderedSkus = ordered.map((o) => o.orderItems.map((i) => i.product.Material_Number)).flat();
    
    const [
      featuredStockMap,          // Record<string, PretSiStocAM[]>
      orderedStockMap,           // Record<string, PretSiStocAM[]>
      featured4thPriceBatch,     // GetPrice4LvlBatch[]
      ordered4thPriceBatch,      // GetPrice4LvlBatch[]
    ] = await Promise.all([
      getPretSiStocBatch(featuredSkus),
      getPretSiStocBatch(orderedSkus),
      has4th
        ? featuredSkus.length > 0 ? getPricesFor4thBatch(featuredSkus, user.userAM || "") : Promise.resolve([])
        : Promise.resolve([]),
      has4th
        ? orderedSkus.length > 0 ?  getPricesFor4thBatch(orderedSkus, user.userAM || "") : Promise.resolve([])
        : Promise.resolve([]),
    ]);
  
    // 4 Merge featured products
    const price4Map = new Map<string, number>(
      featured4thPriceBatch.map((p) => [p.itemno, p.pret])
    );

    const orderedPrice4Map = new Map<string, number>(
      ordered4thPriceBatch.map((p) => [p.itemno, p.pret])
    );
  
    const productsWithData: ProductCardInterface[] = featured.map((p) => {
      const batch = featuredStockMap[p.Material_Number] ?? [];
      const stock = batch.reduce((sum, x) => sum + x.stoc, 0);

      // only look up a 4th-level price if allowed
      const override = has4th
        ? price4Map.get(p.Material_Number)
        : undefined;

      return {
        ...p,
        stock,
        // fallback to DB price if no override (or user not allowed)
        displayPrice: override ?? p.FinalPrice,
      };
    });

    const orderedProductsData: ProductCardInterface[] = ordered.flatMap((o) =>
      o.orderItems.map((item) => {
        const sku = item.product.Material_Number;
        const batch = orderedStockMap[sku] ?? [];
        const stock = batch.reduce((sum, x) => sum + x.stoc, 0);

        const override = has4th
          ? orderedPrice4Map.get(sku)
          : undefined;

        return {
          ...item.product,
          stock,
          displayPrice: override ?? item.product.FinalPrice,
        };
      })
    );    

  return (
    <>
       {/* <HeroSection heroBanners={heroBanners} />

       <CategoryGrid  categories={categories} />  */}

       <ProductGrid products={orderedProductsData} title="Ultimele comandate" description="Produse pe care le-ai cumparat recent" /> 
 
       <ProductGrid products={productsWithData}    title="Produse recomandate"  description="Produse care te-ar putea interesa"  />    
    </>
  );
}


