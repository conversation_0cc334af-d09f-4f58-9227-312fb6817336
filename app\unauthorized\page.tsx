import Link from 'next/link';

export default function UnauthorizedPage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] p-4">
      <div className="text-red-500 mb-4">
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          width="64" 
          height="64" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round"
        >
          <circle cx="12" cy="12" r="10" />
          <line x1="12" y1="8" x2="12" y2="12" />
          <line x1="12" y1="16" x2="12.01" y2="16" />
        </svg>
      </div>
      
      <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
      
      <p className="text-gray-600 mb-6 text-center max-w-md">
        You don't have permission to access this page. Please contact your administrator if you believe this is an error.
      </p>
      
      <div className="flex gap-4">
        <Link 
          href="/"
          className="px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded transition-colors"
        >
          Go Home
        </Link>
        
        <Link 
          href="/contact"
          className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded transition-colors"
        >
          Contact Support
        </Link>
      </div>
    </div>
  );
}