'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function TestSuspensionPage() {
  const [userId, setUserId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleSuspendUser = async () => {
    if (!userId.trim()) {
      setMessage('Please enter a user ID');
      return;
    }

    setIsLoading(true);
    setMessage('');

    try {
      // For testing, we'll directly update the database
      const response = await fetch('/api/admin/suspend-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: userId.trim(),
          reason: 'Test suspension'
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setMessage(`User ${userId} suspended successfully!`);
      } else {
        setMessage(`Error: ${data.error || 'Failed to suspend user'}`);
      }
    } catch (error) {
      setMessage(`Error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleReactivateUser = async () => {
    if (!userId.trim()) {
      setMessage('Please enter a user ID');
      return;
    }

    setIsLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/admin/reactivate-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: userId.trim()
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setMessage(`User ${userId} reactivated successfully!`);
      } else {
        setMessage(`Error: ${data.error || 'Failed to reactivate user'}`);
      }
    } catch (error) {
      setMessage(`Error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Test Account Suspension</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="userId">User ID (Database ID)</Label>
            <Input
              id="userId"
              type="text"
              placeholder="Enter user database ID"
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
            />
          </div>

          {message && (
            <Alert>
              <AlertDescription>{message}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Button 
              onClick={handleSuspendUser}
              disabled={isLoading}
              variant="destructive"
              className="w-full"
            >
              {isLoading ? 'Processing...' : 'Suspend User'}
            </Button>

            <Button 
              onClick={handleReactivateUser}
              disabled={isLoading}
              variant="outline"
              className="w-full"
            >
              {isLoading ? 'Processing...' : 'Reactivate User'}
            </Button>
          </div>

          <div className="text-sm text-muted-foreground">
            <p><strong>Instructions:</strong></p>
            <ol className="list-decimal list-inside space-y-1">
              <li>Enter your user database ID</li>
              <li>Click "Suspend User" to test suspension</li>
              <li>Try to access the app - you should be redirected to suspended page</li>
              <li>Click "Reactivate User" to restore access</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
