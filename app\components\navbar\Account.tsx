"server-only"

import {
  <PERSON>Cart,
  Heart,
  User,
  Truck,
  CreditCard,
  RefreshCw,
  LogOut,
  Wrench,
  Car,
  ShoppingBag,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { SignOutWithLoading } from "./SignOutWithLoading";

type AccountComponentProps = {
  firstName: string;
  email: string;
}

export function AccountComponent({ firstName, email }: AccountComponentProps) {
  return (
    <div className="relative group">
      <Button
        variant="ghost"
        size="icon"
        className="hover:bg-gray-100 dark:hover:bg-gray-800"
      >
        <User className="h-6 w-6 text-[#4D4D4D] dark:text-gray-300" />
      </Button>
      <div className="absolute right-0 mt-2 w-64 py-3 rounded-lg shadow-xl border border-gray-100 dark:border-gray-700 
        bg-white dark:bg-gray-800 opacity-0 invisible 
        group-hover:opacity-100 group-hover:visible transition-all duration-200 transform group-hover:translate-y-0 translate-y-2 divide-y divide-gray-100 dark:divide-gray-700 z-[100]">
        <div className="px-4 py-2">
          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Salut {firstName || 'Unnamed User'}!</p>
          <p className="text-xs text-gray-500 dark:text-gray-400">{email}</p>
        </div>

        <div className="py-2">
          <Link
            href="/account/orders"
            className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2"
          >
            <ShoppingBag className="w-4 h-4 text-gray-400 dark:text-gray-500" />
            Comenzile mele
          </Link>
          <Link
            href="/account/wishlist"
            className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2"
          >
            <Heart className="w-4 h-4 text-gray-400 dark:text-gray-500" />
            Favorite
          </Link>
          <Link
            href="/account/settings"
            className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2"
          >
            <User className="w-4 h-4 text-gray-400 dark:text-gray-500" />
            Profil
          </Link>
        </div>

        <div className="py-2">
          <SignOutWithLoading />
        </div>
      </div>
    </div>
  );
}
