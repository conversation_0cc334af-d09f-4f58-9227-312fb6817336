"server-only"

import { redis } from '@/lib/redis'

const DEFAULT_TTL = 60 * 60 // 1 hour

export const cacheKey = (...parts: (string | number | undefined | null)[]) =>
  parts.filter(Boolean).join(':')

export async function getCachedData<T>(
  key: string,
  fetchFn: () => Promise<T>,
  ttl = getSecondsUntilNextSunday8am() //or DEFAULT_TTL for 1h
): Promise<T> {
  // If Redis is not configured, fetch directly
  if (!redis) {
    console.debug(`[Cache Bypassed] Redis unavailable — fetching directly for ${key}`)
    return fetchFn()
  }

  try {
    const cached = await redis.get<string>(key)

    if (cached) {
      console.debug(`[Cache HIT] ${key}`)
      return cached as T
    }

    console.debug(`[Cache MISS] ${key}`)
    const data = await fetchFn()

    if (data !== undefined && data !== null) {
      await redis.set(key, data, { ex: ttl })
      console.debug(`[Cache SET] ${key} (TTL: ${ttl}s)`)
    }

    return data
  } catch (error) {
    console.error(`[Cache ERROR] ${key}`, error)
    return fetchFn() // fallback to DB if Redis fails
  }
}

export async function invalidateCache(key: string): Promise<void> {
  if (!redis) return

  try {
    await redis.del(key)
    console.debug(`[Cache DEL] ${key}`)
  } catch (error) {
    console.error(`[Cache DEL ERROR] ${key}`, error)
  }
}

function getSecondsUntilNextSunday8am(): number {
  const now = new Date()

  // Get the current day (0 = Sunday, 6 = Saturday)
  const currentDay = now.getDay()

  // Calculate days until next Sunday
  const daysUntilSunday = (7 - currentDay) % 7

  // Set target date to next Sunday 8:00 AM
  const target = new Date(now)
  target.setDate(now.getDate() + daysUntilSunday)
  target.setHours(8, 0, 0, 0)

  // If now is past this Sunday's 8 AM, skip to next week's Sunday
  if (target <= now) {
    target.setDate(target.getDate() + 7)
  }

  const secondsUntil = Math.floor((target.getTime() - now.getTime()) / 1000)
  return secondsUntil
}
