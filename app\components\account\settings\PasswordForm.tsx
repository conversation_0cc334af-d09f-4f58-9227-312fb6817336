"use client";

import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Loader2, CheckCircle, AlertCircle, Eye, EyeOff, Shield, Lock } from "lucide-react";
import { passwordChangeSchema, type PasswordChangeInput } from "@/lib/zod";
import { changePassword, type ActionResult } from "@/app/actions/account";

interface PasswordFormProps {
  lastPasswordChange?: Date | null;
  ssoProvider?: string | null;
}

export default function PasswordForm({ lastPasswordChange, ssoProvider }: PasswordFormProps) {
  const [isPending, startTransition] = useTransition();
  const [result, setResult] = useState<ActionResult | null>(null);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors, isDirty }
  } = useForm<PasswordChangeInput>({
    resolver: zodResolver(passwordChangeSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    }
  });

  const newPassword = watch("newPassword");

  // Password strength calculation
  const calculatePasswordStrength = (password: string): { score: number; feedback: string[] } => {
    const feedback: string[] = [];
    let score = 0;

    if (password.length >= 8) {
      score += 20;
    } else {
      feedback.push("Cel puțin 8 caractere");
    }

    if (/[a-z]/.test(password)) {
      score += 20;
    } else {
      feedback.push("O literă mică");
    }

    if (/[A-Z]/.test(password)) {
      score += 20;
    } else {
      feedback.push("O literă mare");
    }

    if (/\d/.test(password)) {
      score += 20;
    } else {
      feedback.push("O cifră");
    }

    if (/[@$!%*?&]/.test(password)) {
      score += 20;
    } else {
      feedback.push("Un caracter special (@$!%*?&)");
    }

    return { score, feedback };
  };

  const passwordStrength = calculatePasswordStrength(newPassword || "");

  const getStrengthColor = (score: number) => {
    if (score < 40) return "bg-red-500";
    if (score < 60) return "bg-orange-500";
    if (score < 80) return "bg-yellow-500";
    return "bg-green-500";
  };

  const getStrengthText = (score: number) => {
    if (score < 40) return "Slabă";
    if (score < 60) return "Medie";
    if (score < 80) return "Bună";
    return "Foarte bună";
  };

  const onSubmit = (data: PasswordChangeInput) => {
    startTransition(async () => {
      try {
        const result = await changePassword(data);
        setResult(result);
        
        if (result.success) {
          reset(); // Clear form on success
          setTimeout(() => setResult(null), 5000);
        }
      } catch (error) {
        setResult({
          success: false,
          error: "A apărut o eroare neașteptată. Vă rugăm să încercați din nou."
        });
      }
    });
  };

  // If user signed up with SSO, show different UI
  if (ssoProvider) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lock className="h-5 w-5" />
            Schimbă Parola
          </CardTitle>
          <CardDescription>
            Parola este gestionată de furnizorul SSO
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <p>
                  <strong>Parola este gestionată de {ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider}.</strong>
                </p>
                <p className="text-sm text-muted-foreground">
                  Pentru a schimba parola, accesați setările contului dvs. {ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider}.
                </p>
                <div className="mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const urls = {
                        google: 'https://myaccount.google.com/security',
                        microsoft: 'https://account.microsoft.com/security',
                        facebook: 'https://www.facebook.com/settings?tab=security',
                        github: 'https://github.com/settings/security',
                        apple: 'https://appleid.apple.com/account/manage'
                      };
                      window.open(urls[ssoProvider as keyof typeof urls] || '#', '_blank');
                    }}
                  >
                    Accesează setările {ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider}
                  </Button>
                </div>
              </div>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lock className="h-5 w-5" />
          Schimbă Parola
        </CardTitle>
        <CardDescription>
          Actualizați parola pentru a vă menține contul în siguranță
          {lastPasswordChange && (
            <span className="block mt-1 text-xs text-muted-foreground">
              Ultima schimbare: {new Date(lastPasswordChange).toLocaleDateString('ro-RO')}
            </span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Success/Error Messages */}
          {result && (
            <Alert variant={result.success ? "default" : "destructive"}>
              {result.success ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <AlertDescription>
                {result.success 
                  ? "Parola a fost schimbată cu succes!" 
                  : result.error
                }
              </AlertDescription>
            </Alert>
          )}

          {/* Current Password */}
          <div className="space-y-2">
            <Label htmlFor="currentPassword">Parola curentă *</Label>
            <div className="relative">
              <Input
                id="currentPassword"
                type={showCurrentPassword ? "text" : "password"}
                {...register("currentPassword")}
                className={errors.currentPassword ? "border-red-500 pr-10" : "pr-10"}
                placeholder="Introduceți parola curentă"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
              >
                {showCurrentPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {errors.currentPassword && (
              <p className="text-sm text-red-600">{errors.currentPassword.message}</p>
            )}
          </div>

          {/* New Password */}
          <div className="space-y-2">
            <Label htmlFor="newPassword">Parola nouă *</Label>
            <div className="relative">
              <Input
                id="newPassword"
                type={showNewPassword ? "text" : "password"}
                {...register("newPassword")}
                className={errors.newPassword ? "border-red-500 pr-10" : "pr-10"}
                placeholder="Introduceți parola nouă"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowNewPassword(!showNewPassword)}
              >
                {showNewPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {errors.newPassword && (
              <p className="text-sm text-red-600">{errors.newPassword.message}</p>
            )}

            {/* Password Strength Indicator */}
            {newPassword && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Puterea parolei:</span>
                  <span className={`font-medium ${
                    passwordStrength.score < 40 ? 'text-red-600' :
                    passwordStrength.score < 60 ? 'text-orange-600' :
                    passwordStrength.score < 80 ? 'text-yellow-600' :
                    'text-green-600'
                  }`}>
                    {getStrengthText(passwordStrength.score)}
                  </span>
                </div>
                <Progress 
                  value={passwordStrength.score} 
                  className="h-2"
                />
                {passwordStrength.feedback.length > 0 && (
                  <div className="text-xs text-muted-foreground">
                    <p>Parola trebuie să conțină:</p>
                    <ul className="list-disc list-inside ml-2">
                      {passwordStrength.feedback.map((item, index) => (
                        <li key={index}>{item}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Confirm Password */}
          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirmă parola nouă *</Label>
            <div className="relative">
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                {...register("confirmPassword")}
                className={errors.confirmPassword ? "border-red-500 pr-10" : "pr-10"}
                placeholder="Confirmați parola nouă"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {errors.confirmPassword && (
              <p className="text-sm text-red-600">{errors.confirmPassword.message}</p>
            )}
          </div>

          {/* Security Tips */}
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <strong>Sfaturi pentru securitate:</strong>
              <ul className="list-disc list-inside mt-1 text-sm space-y-1">
                <li>Folosiți o parolă unică pentru acest cont</li>
                <li>Nu împărtășiți parola cu nimeni</li>
                <li>Schimbați parola regulat</li>
                <li>Activați autentificarea cu doi factori pentru securitate suplimentară</li>
              </ul>
            </AlertDescription>
          </Alert>

          {/* Field Errors */}
          {result?.fieldErrors && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {Object.entries(result.fieldErrors).map(([field, errors]) => (
                    <li key={field}>
                      <strong>{field}:</strong> {errors.join(", ")}
                    </li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button 
              type="submit" 
              disabled={isPending || !isDirty || passwordStrength.score < 80}
              className="bg-[#0066B1] hover:bg-[#004d85]"
            >
              {isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Se schimbă...
                </>
              ) : (
                "Schimbă parola"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
