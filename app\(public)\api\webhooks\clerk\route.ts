

import { Webhook } from 'svix';
import { headers } from 'next/headers';
import { WebhookEvent } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';
import { getClientIp } from '@/lib/utils';
import { findOrCreateUser } from '@/lib/sync-user';
import { prisma, withRetry } from '@/lib/db';
import { z } from 'zod';
import { logger } from '@/lib/logger';

const clerkUserSchema = z.object({
  id: z.string(),
  email_addresses: z
    .array(
      z.object({
        email_address: z.string().email(),
      })
    )
    .min(1),
  first_name: z.string().nullable(),
  last_name: z.string().nullable(),
  image_url: z.string().url().nullable(),
});

const webhookSchema = z.object({
  data: clerkUserSchema,
  type: z.enum(['user.created', 'user.updated', 'user.deleted']),
  object: z.string(),
});

// Get the webhook secret from environment variables
const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;

export async function POST(req: Request) {

  if (!webhookSecret) {
    logger.warn('[Clerk Webhook] Missing CLERK_WEBHOOK_SECRET');
    return NextResponse.json({ success: false, error: 'Missing webhook secret' }, { status: 500 });
  }

  const clientIp = getClientIp(req);
  const allowedIps = (process.env.ALLOWED_IPS_FOR_HEALTH_CHECK_ENDPOINT || '').split(',').map(ip => ip.trim());

  if (!clientIp || !allowedIps.includes(clientIp)) {
    logger.warn(`[Clerk Webhook] ${clientIp} not allowed`)
    return NextResponse.json({ success: false, error: 'IP not allowed' }, { status: 500 });
  }

  // Verify the webhook signature
  const headerPayload = await headers();
  const svix_id = headerPayload.get('svix-id');
  const svix_timestamp = headerPayload.get('svix-timestamp');
  const svix_signature = headerPayload.get('svix-signature');

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    logger.warn(`[Clerk Webhook] Missing svix headers`)
    return NextResponse.json({ success: false, error: 'Missing svix headers' }, { status: 500 });
  }

  // Get the body
  const rawBody = await req.text();

  // 1. Verify signature
  const wh = new Webhook(webhookSecret);
  let evt: WebhookEvent;

  try {
      // Verify the payload with the headers
    evt = wh.verify(rawBody, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    }) as WebhookEvent;
  }catch (err) {
    logger.error('[Clerk Webhook] Error verifying webhook payload:', err);
    return NextResponse.json({ success: false, error: 'Error verifying webhook payload' }, { status: 500 });
  }

  // 2. Validate with Zod
  try {
    webhookSchema.parse(evt); // or evt.data
  } catch (err) {
    logger.error(`Invalid webhook payload for IP ${clientIp}`)
   return NextResponse.json({ success: false, error: 'Invalid webhook payload' }, { status: 500 });
  }

  // Handle the webhook
  const eventType = evt.type;
  
  try {
    // Create an audit log for all events
    const auditData = {
      action: eventType,
      entityType: 'user',
      entityId: evt.data.id as string,
      details: JSON.stringify(evt.data),
      ipAddress: headerPayload.get('x-forwarded-for') || null,
      userAgent: headerPayload.get('user-agent') || null,
    };

    switch (eventType) {

      case 'user.created': {
        const { id, email_addresses, first_name, last_name, image_url } = evt.data;
        
        await findOrCreateUser({
          externalId: id as string,
          email: email_addresses?.[0]?.email_address || '',
          firstName: first_name || '',
          lastName: last_name || '',
          profileImage: image_url || '',
          updateLoginStats: false, // Don't update login stats for webhook events
        });
        
        // Create audit log
        await withRetry(() => prisma.userAuditLog.create({
          data: auditData
        }))
        
        logger.info(`[Clerk Webhook] user.created received for ID: ${id}`);
        break;
      }
      
      case 'user.updated': {
        const { id, email_addresses, first_name, last_name, image_url } = evt.data;
        
        // Update the user in the database
        await withRetry(() => prisma.user.update({
          where: { externalId: id as string },
          data: {
            email: email_addresses?.[0]?.email_address || '',
            firstName: first_name || '',
            lastName: last_name || '',
            profileImage: image_url || '',
            updatedAt: new Date(),
          },
        }))

        await withRetry(() => prisma.userAuditLog.create({
          data: auditData
        }))
        
        logger.info(`[Clerk Webhook] user.updated received for ID: ${id}`);
        break;
      }
      
      case 'user.deleted': {
        const { id } = evt.data;
        
        // Create the audit log first (before the user is deleted)
        await withRetry(() => prisma.userAuditLog.create({
          data: auditData
        }))
        
        // Delete the user from the database
        await withRetry(() => prisma.user.delete({
          where: { externalId: id as string },
        }))
        
        logger.info(`[Clerk Webhook] user.deleted received for ID: ${id}`);
        break;
      }
      
      // Handle other events as needed
      default:
        // For any other events, just log them
        await withRetry(() => prisma.userAuditLog.create({
          data: {
            ...auditData,
            action: `unhandled.${eventType}`
          }
        }))
        logger.info(`[Clerk Webhook] default case received for ID: ${eventType}`);
        break;
    }
    
    return NextResponse.json({ success: true }, { status: 200 });
  } catch (error) {
    logger.error('Error processing webhook:', error);
    
    // Log the error to the database
    try {
      await withRetry(() => prisma.userAuditLog.create({
        data: {
          action: `error.${eventType}`,
          entityType: 'clerkWebhook',
          entityId: evt.data.id as string || null,
          details: JSON.stringify({ error, event: evt.data }),
          ipAddress: headerPayload.get('x-forwarded-for') || null,
          userAgent: headerPayload.get('user-agent') || null,
        }
      }))
    } catch (logError) {
      logger.error('Failed to log error to database:', logError);
    }
    return NextResponse.json({ success: false, error: 'Error processing webhook' }, { status: 500 });
  }
}