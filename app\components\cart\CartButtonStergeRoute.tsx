"use client"

import { deleteItemFromCart } from "@/app/actions/cart";
import { Button } from "@/components/ui/button";
import { CartItem } from "@/types/cart";
import { Loader, Loader2, Trash } from "lucide-react";
import { useTransition } from "react";
import { toast } from "sonner";

export function CartButtonStergeRoute( { item }: { item: CartItem }) {
    const [isPending, startTransition] = useTransition();

      const handleDeleteItem = async () => {
        startTransition(async () => {
          try {
            const response = await deleteItemFromCart(item.id);
      
            if (response.success) {
              toast.success("Sters cu succes din cos.");
            } else {
              toast.error("Eșec la actualizare.");
            }
          } catch (err) {
            toast.error("Nu s-a putut actualiza produsul.");
          }
        });
      };
  return (
    <>
          {isPending ? (
            <Button
              variant="outline"
              size="sm"
              className="gap-2 text-red-600 hover:text-red-700"
              disabled
            >
              <Loader2 className="w-4 h-4 animate-spin" /> Sterge
            </Button>
          ) : (
            <Button
              variant="outline"
              size="sm"
              className="gap-2 text-red-600 hover:text-red-700"
              onClick={handleDeleteItem}
            >
              <Trash className="w-4 h-4" /> Sterge
            </Button>

          )}
    </>
  );
}