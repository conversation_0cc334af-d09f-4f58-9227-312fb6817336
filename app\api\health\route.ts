import { NextResponse } from 'next/server';
import { prisma, withRetry } from '@/lib/db';
import { logger } from '@/lib/logger';
import { redis } from '@/lib/redis';
import { getClientIp } from '@/lib/utils';

//endpoint for monitoring 
export async function GET(req: Request) {

  const clientIp = getClientIp(req);
  const allowedIps = (process.env.ALLOWED_IPS_FOR_HEALTH_CHECK_ENDPOINT || '').split(',').map(ip => ip.trim());

  if (!clientIp || !allowedIps.includes(clientIp)) {
    logger.warn(`[Health Api] IP not allowed: ${clientIp}`)
    return NextResponse.json({ status: 'forbidden', reason: 'IP not allowed' }, { status: 403 });
  }

  const url = new URL(req.url);
  const apiKey = url.searchParams.get(process.env.HEALTH_CHECK_API_KEY as string);

  // 🔐 Secure: Check if API key is valid
  if (apiKey !== process.env.HEALTH_CHECK_API_VALUE) {
      logger.warn(`[Health Api] apiKey is missing: ${clientIp}`)
    return NextResponse.json({ status: 'unauthorized' }, { status: 401 });
  }

  try {
    // Check database connection
    await withRetry(() => prisma.$queryRaw`SELECT 1`);

    const redisStatus = await redis?.ping().then(() => 'connected to redis').catch(() => 'disconnected from redis');
    
    return NextResponse.json({ 
      status: 'ok',
      timestamp: new Date().toISOString(),
      services: {
        database: 'connected to database',
        redis: redisStatus
      }
    }, 
    { 
      status: 200,  
      headers: { 'Cache-Control': 'no-store' }, 
    }
    );
  } catch (error) {
    logger.error('[Health Api] Health check failed', error as Error, { context: 'health-check' });
    
    return NextResponse.json({ 
      status: 'error',
      timestamp: new Date().toISOString(),
      services: {
        database: 'disconnected from database',
        redis: 'disconnected from redis'
      },
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    }, { status: 500 });
  }
}