'use client';

import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Pause, AlertTriangle, CheckCircle, AlertCircle } from "lucide-react";
import { suspendAccount } from "@/app/actions/account";

export default function AccountSuspendPage() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [isSuspending, setIsSuspending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [reason, setReason] = useState('');
  const [confirmations, setConfirmations] = useState({
    understand: false,
    dataRetention: false,
    reactivation: false
  });

  // useEffect(() => {
  //   if (isLoaded && !user) {
  //     router.push('/sign-in');
  //   }
  // }, [isLoaded, user, router]);

  const canSuspend = Object.values(confirmations).every(Boolean) && reason.trim().length > 0;

  const handleSuspend = async () => {
    if (!user || !canSuspend) return;

    setIsSuspending(true);
    setError(null);

    try {
      const result = await suspendAccount({ reason: reason.trim() });
      
      if (result.success) {
        setSuccess(true);
        // Redirect to sign-in after suspension
        setTimeout(() => {
          window.location.href = '/sign-in?message=account-suspended';
        }, 3000);
      } else {
        setError(result.error || 'A apărut o eroare la suspendarea contului.');
      }
    } catch (err: any) {
      console.error('Account suspension error:', err);
      setError('A apărut o eroare neașteptată. Vă rugăm să încercați din nou.');
    } finally {
      setIsSuspending(false);
    }
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-orange-100">
            <Pause className="h-6 w-6 text-orange-600" />
          </div>
          <CardTitle>Suspendare Cont</CardTitle>
          <CardDescription>
            Suspendați temporar contul dvs. - puteți reactiva oricând
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Contul a fost suspendat cu succes. Veți fi redirecționat la pagina de autentificare.
              </AlertDescription>
            </Alert>
          )}

          {!success && (
            <>
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Ce se întâmplă când suspendați contul:</strong>
                  <ul className="mt-2 space-y-1 list-disc list-inside text-sm">
                    <li>Nu veți mai putea accesa contul</li>
                    <li>Datele dvs. vor fi păstrate în siguranță</li>
                    <li>Puteți reactiva contul oricând contactând suportul</li>
                    <li>Comenzile în curs nu vor fi afectate</li>
                  </ul>
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="reason">Motivul suspendării (opțional)</Label>
                  <Textarea
                    id="reason"
                    placeholder="Vă rugăm să ne spuneți de ce doriți să suspendați contul..."
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    className="mt-2"
                    rows={3}
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="understand"
                      checked={confirmations.understand}
                      onCheckedChange={(checked) => 
                        setConfirmations(prev => ({ ...prev, understand: !!checked }))
                      }
                    />
                    <Label htmlFor="understand" className="text-sm">
                      Înțeleg că nu voi mai putea accesa contul după suspendare
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="dataRetention"
                      checked={confirmations.dataRetention}
                      onCheckedChange={(checked) => 
                        setConfirmations(prev => ({ ...prev, dataRetention: !!checked }))
                      }
                    />
                    <Label htmlFor="dataRetention" className="text-sm">
                      Înțeleg că datele mele vor fi păstrate pentru reactivare
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="reactivation"
                      checked={confirmations.reactivation}
                      onCheckedChange={(checked) => 
                        setConfirmations(prev => ({ ...prev, reactivation: !!checked }))
                      }
                    />
                    <Label htmlFor="reactivation" className="text-sm">
                      Știu că pot reactiva contul contactând suportul
                    </Label>
                  </div>
                </div>

                <div className="flex space-x-3">
                  <Button 
                    variant="destructive"
                    onClick={handleSuspend}
                    disabled={isSuspending || !canSuspend}
                    className="flex-1"
                  >
                    {isSuspending ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Se suspendă...
                      </>
                    ) : (
                      'Suspendă contul'
                    )}
                  </Button>

                  <Button 
                    variant="outline" 
                    onClick={() => {
                      window.close();
                      router.push('/account/settings?tab=security');
                    }}
                    className="flex-1"
                  >
                    Anulează
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
