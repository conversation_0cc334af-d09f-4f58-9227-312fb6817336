import { z } from 'zod'; // For input validation

export const FamilyCodeSchema = z.object({
    familyCode: z.
        string().
        min(4, { message: 'Family code must be at least 4 characters long' }).
        max(10, { message: 'Family code must be at most 10 characters long' })
});


//export const userIdStringSchema = z.string().min(24);
export const productCodSchema = z.string().length(11)
export const cuidSchema =   z.string().cuid()
export const userIdClerkSchema =  z.string().startsWith('user_')

export const addObservationsToCartSchema = z.string()

export const updateCartSchema = z.object({
    itemId: cuidSchema.optional(),
    vinNotes: z.string().optional(),
    addVinNotesToInvoice: z.boolean().optional(),
    addToOrder: z.boolean().optional(),
    quantity: z.number().optional(),
    orderNotes: z.string().optional(),
});


export const categoryIdSchemaMSSQL = z.string().min(7).max(20) // Alphanumeric with dashes only
//export const categoryIdSchemaMSSQL = z.string().min(1).max(20).regex(/^[A-Z0-9-]+$/i) // Alphanumeric with dashes only

// Account Profile Validation Schemas
export const profileUpdateSchema = z.object({
  firstName: z.string()
    .min(1, { message: 'Prenumele este obligatoriu' })
    .max(50, { message: 'Prenumele nu poate avea mai mult de 50 de caractere' })
    .regex(/^[a-zA-ZăâîșțĂÂÎȘȚ\s-']+$/, { message: 'Prenumele poate conține doar litere, spații, cratimă și apostrof' }),

  lastName: z.string()
    .min(1, { message: 'Numele de familie este obligatoriu' })
    .max(50, { message: 'Numele de familie nu poate avea mai mult de 50 de caractere' })
    .regex(/^[a-zA-ZăâîșțĂÂÎȘȚ\s-']+$/, { message: 'Numele de familie poate conține doar litere, spații, cratimă și apostrof' }),

  email: z.string()
    .email({ message: 'Adresa de email nu este validă' })
    .max(255, { message: 'Adresa de email nu poate avea mai mult de 255 de caractere' }),

  phoneNumber: z.string()
    .optional()
    .refine((val) => !val || /^(\+4|0)[0-9]{9}$/.test(val), {
      message: 'Numărul de telefon trebuie să fie în format românesc valid (ex: +40712345678 sau 0712345678)'
    }),

  bio: z.string()
    .max(500, { message: 'Biografia nu poate avea mai mult de 500 de caractere' })
    .optional(),

  jobTitle: z.string()
    .max(100, { message: 'Titlul postului nu poate avea mai mult de 100 de caractere' })
    .optional(),

  department: z.string()
    .max(100, { message: 'Departamentul nu poate avea mai mult de 100 de caractere' })
    .optional(),

  salutation: z.enum(['Dl', 'Dna']).optional(),

  preferredLanguage: z.string()
    .max(10, { message: 'Limba preferată nu poate avea mai mult de 10 caractere' })
    .optional(),

  timezone: z.string()
    .max(50, { message: 'Fusul orar nu poate avea mai mult de 50 de caractere' })
    .optional(),
});

export const passwordChangeSchema = z.object({
  currentPassword: z.string()
    .min(1, { message: 'Parola curentă este obligatorie' }),

  newPassword: z.string()
    .min(8, { message: 'Parola nouă trebuie să aibă cel puțin 8 caractere' })
    .max(128, { message: 'Parola nouă nu poate avea mai mult de 128 de caractere' })
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
      message: 'Parola nouă trebuie să conțină cel puțin o literă mică, o literă mare, o cifră și un caracter special'
    }),

  confirmPassword: z.string()
    .min(1, { message: 'Confirmarea parolei este obligatorie' }),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: 'Parolele nu se potrivesc',
  path: ['confirmPassword'],
});

export const notificationPreferencesSchema = z.object({
  emailNotifications: z.boolean(),
  pushNotifications: z.boolean(),
  smsNotifications: z.boolean(),
  newsletterOptIn: z.boolean(),
});

export const securitySettingsSchema = z.object({
  twoFactorEnabled: z.boolean(),
});

// Type exports for TypeScript
export type ProfileUpdateInput = z.infer<typeof profileUpdateSchema>;
export type PasswordChangeInput = z.infer<typeof passwordChangeSchema>;
export type NotificationPreferencesInput = z.infer<typeof notificationPreferencesSchema>;
export type SecuritySettingsInput = z.infer<typeof securitySettingsSchema>;

