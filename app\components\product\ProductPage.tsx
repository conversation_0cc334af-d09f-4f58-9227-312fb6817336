"use client"


import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Heart, Home, MapPin } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
// import RelatedProductGrid from "@/app/components/storefront/RelatedProductGrid";
import Link from "next/link";
import { Input } from "@/components/ui/input";
import Image from "next/image";
import { cn, formatDiscount, formatPriceRON, getLocationDetails, getStockStatus, getStockStatusData, translateUnit } from "@/lib/utils";
import { ProductPage } from "@/types/product";
import WishlistButton from "../wishlist/WishlistButton";
import WishlistButtonRoute from "../wishlist/WishlistButtonRoute";
import CartButtonRoute from "../cart/CartButtonRoute";
import { PretSiStocAM } from "@/types/mssql";

export default function ProductPageContent({ product, mainStockArr, displayPrice }: { product: ProductPage, mainStockArr: PretSiStocAM[], displayPrice: number | null }) {

  const stock = mainStockArr.reduce((sum, x) => sum + x.stoc, 0);
  const { statusText, dotColorClass } = getStockStatusData(stock);
  const mainStock = mainStockArr.filter( x => x.stoc > 0); //only show locations with stock
  const [quantity, setQuantity] = useState(1);
  const [isStockDialogOpen, setIsStockDialogOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(0);

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value > 0) {
      setQuantity(value);
    }
  };

  const handleIncrement = () => {
    setQuantity((prev) => prev + 1);
  };

  const handleDecrement = () => {
    if (quantity > 1) {
      setQuantity((prev) => prev - 1);
    }
  };
      return (<>
    <div className="max-w-[1640px] mx-auto px-4 sm:px-6 lg:px-8">
      {/* Breadcrumbs */}
      <nav className="text-sm my-8">
        <ol className="flex items-center space-x-2">
          <li>
            <Link
              href="/"
              className="text-muted-foreground hover:text-foreground transition"
            >
              <Home className="inline-block w-4 h-4" />
            </Link>
          </li>
          <li>
            <span className="text-muted-foreground hover:text-foreground transition">/</span>
          </li>
          {product.categoryLevel3?.name && (
            <>
              <li>
                <Link
                  href={`/${product.categoryLevel3.name}`}
                  className="text-muted-foreground hover:text-foreground transition"
                >
                  {product.categoryLevel3.name}
                </Link>
              </li>
              <li>
                <span className="text-muted-foreground hover:text-foreground transition">/</span>
              </li>
            </>
          )}
          <li className="text-foreground">{product.Description_Local}</li>
        </ol>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Left-side Product Image */}
        <div className="space-y-4">
          <div className="w-full lg:max-w-xl aspect-w-4 aspect-h-3 bg-gray-100 rounded-lg overflow-hidden">
            <Image
              src={product.ImageUrl[selectedImage]}
              alt={product.Description_Local ?? ""}
              className="w-full h-full object-cover"
              width={330}
              height={330}
            />
          </div>

          {/* Thumbnails */}
          <div className="flex gap-2 overflow-x-auto">
            {product.ImageUrl.map((img, index) => (
              <Button
                key={index}
                className={`w-20 h-20 rounded-md overflow-hidden border-2 ${selectedImage === index ? "border-[#0066B1]" : "border-transparent"}`}
                onClick={() => setSelectedImage(index)}
              >
                <Image
                  src={img}
                  alt={`Thumbnail ${index + 1}`}
                  className="w-full h-full object-cover"
                  width={80}
                  height={80}
                />
              </Button>
            ))}
          </div>
        </div>

        {/*Right-side Product Info */}
        <div className="space-y-6">

          {/* Title */}
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              {product.Description_Local}
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              OE: {product.Material_Number}
            </p>
          </div>
          
          {/* Price */}
          <div className="flex items-baseline gap-4">
            <span className="text-3xl font-bold text-red-600">
              {formatPriceRON(displayPrice ?? product.FinalPrice)}
            </span>
            {product.HasDiscount && ( <>
              <span className="text-xl text-gray-500 line-through">
              {formatPriceRON(product.PretAM)}
            </span>
            <span className="bg-red-100 text-red-800 text-sm font-medium px-2.5 py-0.5 rounded-full">
              {formatDiscount(product.activeDiscountType, product.activeDiscountValue)}
            </span> </>)}
          </div>

          {/* Stock Status */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              {/* The dot's color is now dynamic */}
              <div className={`w-3 h-3 rounded-full ${dotColorClass}`}></div>
              {/* The status text is also dynamic */}
              <span className="text-sm font-medium">{statusText}</span>
            </div>
            
            <Button
              variant="link"
              disabled={stock === 0}
              onClick={() => setIsStockDialogOpen(true)}
              className={cn(
                "p-0 h-auto font-medium transition-colors",
                stock === 0
                  ? "text-muted-foreground cursor-not-allowed hover:text-muted-foreground"
                  : "text-[#0066B1] hover:text-[#004d85]"
              )}
            >
              Vezi stoc
            </Button>
          </div>

          {/* Product Tabs */}
          <Tabs defaultValue="models" className="w-full mt-6">
              <TabsList className="grid grid-cols-3 mb-4">
                <TabsTrigger value="models">Model</TabsTrigger>
                <TabsTrigger value="submodels">Submodel-Static</TabsTrigger> 
                <TabsTrigger value="engines">Motor-Static</TabsTrigger>
              </TabsList>

              <TabsContent value="models" className="p-4 border rounded-md">
                <h3 className="font-medium mb-2">Modele Compatibile</h3>
                <div className="grid grid-cols-5 gap-2">
                  {product.productClass?.vehicleModels.map((model, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                      <span>{model.vehicleModel.name}</span>
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="submodels" className="p-4 border rounded-md">
                <h3 className="font-medium mb-2">Submodele Compatibile</h3>
                <div className="grid grid-cols-5 gap-2">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                    <span>F30</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                    <span>F32</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                    <span>F10</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                    <span>G20</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                    <span>G22</span>
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="engines" className="p-4 border rounded-md">
                <h3 className="font-medium mb-2">Motoare Compatibile</h3>
                <div className="grid grid-cols-5 gap-2">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                    <span>N47</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                    <span>B47</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                    <span>N57</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                    <span>S55</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                    <span>S58</span>
                  </div>
                </div>
              </TabsContent>
          </Tabs>

          {/* Add to Cart Section */}
          <div className="pt-6 border-t border-gray-200">
            <div className="flex items-center gap-4">
            {/* <div className="flex items-center">
                <Button
                  variant="outline"
                  onClick={handleDecrement}
                  //className="w-10 h-10 flex items-center justify-center rounded-l-md bg-gray-100 hover:bg-gray-200 transition-colors"
                >
                  −
                </Button>
                <Input
                  type="text"
                  value={quantity}
                  onChange={handleQuantityChange}
                  className="w-12 h-10 text-center border-y focus:outline-none focus:ring-0"
                  min="1"
                />
                <Button
                  variant="outline"
                  onClick={handleIncrement}
                  //className="w-10 h-10 flex items-center justify-center rounded-r-md bg-gray-100 hover:bg-gray-200 transition-colors"
                >
                  +
                </Button>
              </div> */}

              {/* <Button className="flex-1 bg-[#0066B1] hover:bg-[#004d85] text-white h-10">
                Adauga in cos
              </Button> */}
              <CartButtonRoute product={product.Material_Number} />
              <WishlistButtonRoute material_number={product.Material_Number} />
            </div>
          </div>
        </div>

        {/* Product Information */}
        { product.Net_Weight || product.Base_Unit_Of_Measur || product.New_Material || product.classCode ? (
        <div className="p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-foreground mb-4">
            Informatii
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

            {/* Greutate */}
            { product.Net_Weight && (
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-sm font-medium">
                Greutate
              </span>
              <span className="text-sm">{product.Net_Weight} kg</span>
            </div> )}
            {/*  Unitate de masura */}
            { product.Base_Unit_Of_Measur && (
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-sm font-medium">
                Unitate de masura
              </span>
              <span className="text-sm ">{translateUnit(product.Base_Unit_Of_Measur ?? '')}</span>
            </div> )}
            {/* New Material */}
            { product.New_Material && (
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-sm font-medium">
                Cod inlocuitor
              </span>
              <span className="text-sm ">{product.New_Material}</span>
            </div> )}
            {/* Clasa */}
            { product.classCode && (
            <div  className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-sm font-medium">
                Clasa
              </span>
              <span className="text-sm ">{product.classCode}</span>
            </div> )}
          </div>
        </div> ) : null }
        
        {/* Product Attributes */}
        { product.attributes.length > 0 ? (
        <div className="p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-foreground mb-4">
            Atribute
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {product.attributes.map((attribute, index) => (
              <div
                key={index}
                className="flex justify-between items-center py-2 border-b border-gray-200"
              >
                <span className="text-sm font-medium ">
                  {attribute.key}
                </span>
                <span className="text-sm ">{attribute.value}</span>
              </div>
            ))}
          </div>
        </div> ) : null }
      </div>

      {/* Stock Location Dialog */}
       <Dialog open={isStockDialogOpen} onOpenChange={setIsStockDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Stoc Disponibil</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {mainStock.map((location, index) => {
              const details = getLocationDetails(location.locatie);
              return (
                  <div
                    key={index}
                    className="flex items-start gap-4 p-4 rounded-lg border border-gray-200"
                  >
                    <div className="p-2 rounded-full bg-gray-100">
                      <MapPin className="w-5 h-5 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      {/* Display the friendly name */}
                      <h3 className="font-medium">{details.name}</h3>
                      {/* Display the dummy address */}
                      <p className="text-sm text-gray-500">{details.address}</p>
                      <p
                        className={`text-sm mt-1 ${location.stoc > 0 ? "text-green-600" : "text-red-600"}`}
                      >
                        {location.stoc > 0
                          ? `${location.stoc} in stock`
                          : "Out of stock"}
                      </p>
                    </div>
                  </div>
                );
            })}
          </div>
        </DialogContent>
      </Dialog> 

      {/* <RelatedProductGrid /> */}
    </div>
  </>);
}