import type { 
  OrderStatus as PrismaOrderStatus,
  PaymentStatus as PrismaPaymentStatus,
  PaymentMethod as PrismaPaymentMethod,
  ShippingMethod as PrismaShippingMethod,
  ShipmentStatus as PrismaShipmentStatus,
  Showroom as PrismaShowroom,
  DiscountType as PrismaDiscountType
} from "@/generated/prisma";

// Order Product Interface for UI
export interface OrderProduct {
  id: string;
  name: string;
  oeCode: string;
  image: string;
  quantity: number;
  price: number;
  hasDiscount: boolean;
  discountPercentage: number | null;
  activeDiscountType: PrismaDiscountType | null;
  activeDiscountValue: number | null;
}

// Billing Address Interface
export interface OrderBillingAddress {
  fullName: string;
  companyName?: string;
  address: string;
  city: string;
  county: string;
  cui?: string;
  bank?: string;
  iban?: string;
}

// Shipping Address Interface
export interface OrderShippingAddress {
  fullName: string;
  address: string;
  city: string;
  county: string;
  phoneNumber: string;
  notes?: string;
}

// Shipping History Interface
export interface ShippingHistoryEvent {
  date: string;
  status: string;
  location: string;
  description: string;
}

// Order Interface for UI
export interface Order {
  id: string;
  orderNumber: string;
  date: string;
  total: number;
  status: string;
  orderStatus: PrismaOrderStatus;
  paymentStatus: PrismaPaymentStatus;
  paymentMethod: PrismaPaymentMethod;
  shippingMethod: PrismaShippingMethod;
  shipmentStatus: PrismaShipmentStatus;
  showroom?: PrismaShowroom;
  isPaid: boolean;
  items: OrderProduct[];
  billing?: OrderBillingAddress;
  shipping?: OrderShippingAddress;
  tracking?: string;
  estimatedDelivery?: string;
  currentLocation?: string;
  shippingHistory?: ShippingHistoryEvent[];
  notes?: string;
  vin?: string;
  invoiceAM?: string;
  // Timestamps
  placedAt: string;
  processedAt?: string;
  completedAt?: string;
  cancelledAt?: string;
  shippingProcessedAt?: string;
  shippedAt?: string;
  deliveredAt?: string;
  paidAt?: string;
  refundedAt?: string;
}

// Raw Order Item from Prisma (for data fetching)
export interface RawOrderItemFromPrisma {
  id: string;
  quantity: number;
  price: any; // Prisma Decimal type
  notes?: string | null;
  vinOrderItem?: string | null;
  product: {
    id: string;
    Material_Number: string;
    Description_Local: string | null;
    PretAM: any | null; // Prisma Decimal type
    FinalPrice: any | null; // Prisma Decimal type
    ImageUrl: string[];
    HasDiscount: boolean;
    discountPercentage: any | null; // Prisma Decimal type
    activeDiscountType: PrismaDiscountType | null;
    activeDiscountValue: any | null; // Prisma Decimal type
  };
}

// Raw Order from Prisma (for data fetching)
export interface RawOrderFromPrisma {
  id: string;
  orderNumber: string;
  amount: any; // Prisma Decimal type
  isPaid: boolean;
  vin?: string | null;
  invoiceAM?: string | null;
  notes?: string | null;
  orderStatus: PrismaOrderStatus;
  paymentStatus: PrismaPaymentStatus;
  paymentMethod: PrismaPaymentMethod;
  shippingMethod: PrismaShippingMethod;
  shipmentStatus: PrismaShipmentStatus;
  showroom?: PrismaShowroom | null;
  placedAt: Date;
  processedAt?: Date | null;
  completedAt?: Date | null;
  cancelledAt?: Date | null;
  shippingProcessedAt?: Date | null;
  shippedAt?: Date | null;
  deliveredAt?: Date | null;
  paidAt?: Date | null;
  refundedAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
  orderItems: RawOrderItemFromPrisma[];
  billingAddress?: {
    fullName: string;
    companyName?: string | null;
    address: string;
    city: string;
    county: string;
    cui?: string | null;
    bank?: string | null;
    iban?: string | null;
  } | null;
  shippingAddress?: {
    fullName: string;
    address: string;
    city: string;
    county: string;
    phoneNumber: string;
    notes?: string | null;
  } | null;
}

// Order List Response Interface
export interface OrdersResponse {
  orders: Order[];
  pagination: {
    total: number;
    pages: number;
    currentPage: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Order Filters Interface
export interface OrderFilters {
  status?: string;
  search?: string;
  page?: number;
  limit?: number;
  dateFrom?: string;
  dateTo?: string;
}

// Order Status Mapping
export const ORDER_STATUS_LABELS: Record<PrismaOrderStatus, string> = {
  plasata: "Placed",
  procesare: "Processing", 
  confirmata: "Confirmed",
  pregatita: "Prepared",
  expediata: "Shipped",
  livrata: "Delivered",
  completa: "Complete",
  anulata: "Cancelled",
  stornata: "Voided",
  returnata: "Returned",
  partiala: "Partially Fulfilled"
};

// Payment Status Mapping
export const PAYMENT_STATUS_LABELS: Record<PrismaPaymentStatus, string> = {
  asteptare: "Pending",
  succes: "Success",
  esuat: "Failed",
  rambursat: "Refunded",
  partial_rambursat: "Partially Refunded",
  contestat: "Disputed"
};

// Shipment Status Mapping
export const SHIPMENT_STATUS_LABELS: Record<PrismaShipmentStatus, string> = {
  asteptare: "Pending",
  prelucrare: "Processing",
  pregatit: "Ready",
  expediat: "Shipped",
  tranzit: "In Transit",
  livrat: "Delivered",
  esuat: "Failed",
  intors: "Returned",
  anulat: "Cancelled",
  partial: "Partially Shipped"
};

// Payment Method Mapping
export const PAYMENT_METHOD_LABELS: Record<PrismaPaymentMethod, string> = {
  ramburs: "Cash on Delivery",
  card: "Credit Card",
  transfer: "Bank Transfer",
  laTermen: "Payment Terms"
};

// Shipping Method Mapping
export const SHIPPING_METHOD_LABELS: Record<PrismaShippingMethod, string> = {
  curier: "Courier",
  showroom: "Pickup from Showroom"
};
